# https://help.github.com/en/categories/automating-your-workflow-with-github-actions

name: "Code coverage"

on:
  push:
    branches:
      - "development"
      - "coverage"

jobs:

  coverage:

    name: "Code coverage"

    runs-on: ${{ matrix.operating-system }}

    strategy:
      matrix:
        php-version:
          - "7.4"

        operating-system: [ubuntu-latest]

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "xdebug"
          php-version: "${{ matrix.php-version }}"
          extensions: "mbstring, gd, bcmath, bz2"
          tools: composer:v2

      - name: "Install dependencies"
        run: "composer install --no-interaction --no-progress"

      - name: "Code coverage"
        run: composer coverage
