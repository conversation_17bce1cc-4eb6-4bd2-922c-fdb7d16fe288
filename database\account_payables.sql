-- สคีมาสำหรับระบบบัญชีเจ้าหนี้ (Account Payable System)

-- ตารางสำหรับบัญชีเจ้าหนี้หลัก
CREATE TABLE `account_payables` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ap_number` varchar(50) NOT NULL COMMENT 'เลขที่บัญชีเจ้าหนี้',
  `supplier_id` int(11) NOT NULL COMMENT 'รหัสเจ้าหนี้/ผู้ขาย',
  `bill_id` int(11) DEFAULT NULL COMMENT 'รหัสใบวางบิล (เชื่อมโยงกับตาราง bills)',
  `invoice_number` varchar(100) DEFAULT NULL COMMENT 'เลขที่ใบแจ้งหนี้จากผู้ขาย',
  `bill_date` date NOT NULL COMMENT 'วันที่ใบวางบิล',
  `due_date` date NOT NULL COMMENT 'วันที่ครบกำหนดชำระ',
  `amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินรวม',
  `vat_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนภาษีมูลค่าเพิ่ม',
  `withholding_tax` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'ภาษีหัก ณ ที่จ่าย',
  `net_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินสุทธิ',
  `paid_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินที่จ่ายแล้ว',
  `remaining_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินคงเหลือ',
  `status` enum('pending','partial_paid','paid','overdue','cancelled') NOT NULL DEFAULT 'pending' COMMENT 'สถานะ',
  `description` text DEFAULT NULL COMMENT 'หมายเหตุ',
  `created_by` int(11) NOT NULL COMMENT 'ผู้สร้าง',
  `updated_by` int(11) DEFAULT NULL COMMENT 'ผู้แก้ไขล่าสุด',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_bill_id` (`bill_id`),
  KEY `idx_status` (`status`),
  KEY `idx_due_date` (`due_date`),
  UNIQUE KEY `unique_ap_number` (`ap_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


