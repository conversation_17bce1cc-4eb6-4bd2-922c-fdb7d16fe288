-- สคีมาสำหรับระบบบัญชีเจ้าหนี้ (Account Payable System)

-- ตารางสำหรับบัญชีเจ้าหนี้หลัก
CREATE TABLE `account_payables` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ap_number` varchar(50) NOT NULL COMMENT 'เลขที่บัญชีเจ้าหนี้',
  `supplier_id` int(11) NOT NULL COMMENT 'รหัสเจ้าหนี้/ผู้ขาย',
  `bill_id` int(11) DEFAULT NULL COMMENT 'รหัสใบวางบิล (เชื่อมโยงกับตาราง bills)',
  `invoice_number` varchar(100) DEFAULT NULL COMMENT 'เลขที่ใบแจ้งหนี้จากผู้ขาย',
  `bill_date` date NOT NULL COMMENT 'วันที่ใบวางบิล',
  `due_date` date NOT NULL COMMENT 'วันที่ครบกำหนดชำระ',
  `amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินรวม',
  `vat_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนภาษีมูลค่าเพิ่ม',
  `withholding_tax` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'ภาษีหัก ณ ที่จ่าย',
  `net_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินสุทธิ',
  `paid_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินที่จ่ายแล้ว',
  `remaining_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินคงเหลือ',
  `status` enum('pending','partial_paid','paid','overdue','cancelled') NOT NULL DEFAULT 'pending' COMMENT 'สถานะ',
  `description` text DEFAULT NULL COMMENT 'หมายเหตุ',
  `created_by` int(11) NOT NULL COMMENT 'ผู้สร้าง',
  `updated_by` int(11) DEFAULT NULL COMMENT 'ผู้แก้ไขล่าสุด',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_bill_id` (`bill_id`),
  KEY `idx_status` (`status`),
  KEY `idx_due_date` (`due_date`),
  UNIQUE KEY `unique_ap_number` (`ap_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางสำหรับการชำระหนี้
CREATE TABLE `account_payable_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `payment_number` varchar(50) NOT NULL COMMENT 'เลขที่การชำระเงิน',
  `account_payable_id` int(11) NOT NULL COMMENT 'รหัสบัญชีเจ้าหนี้',
  `payment_date` date NOT NULL COMMENT 'วันที่จ่ายเงิน',
  `payment_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินที่จ่าย',
  `payment_method` enum('cash','bank_transfer','cheque','credit_card','other') NOT NULL DEFAULT 'bank_transfer' COMMENT 'วิธีการจ่ายเงิน',
  `bank_account` varchar(100) DEFAULT NULL COMMENT 'บัญชีธนาคาร',
  `cheque_number` varchar(50) DEFAULT NULL COMMENT 'เลขที่เช็ค',
  `transaction_ref` varchar(100) DEFAULT NULL COMMENT 'เลขที่อ้างอิงธุรกรรม',
  `description` text DEFAULT NULL COMMENT 'หมายเหตุ',
  `created_by` int(11) NOT NULL COMMENT 'ผู้สร้าง',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_account_payable_id` (`account_payable_id`),
  KEY `idx_payment_date` (`payment_date`),
  UNIQUE KEY `unique_payment_number` (`payment_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางสำหรับการปรับปรุงหนี้ (Credit/Debit Memo)
CREATE TABLE `account_payable_adjustments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `adjustment_number` varchar(50) NOT NULL COMMENT 'เลขที่การปรับปรุง',
  `account_payable_id` int(11) NOT NULL COMMENT 'รหัสบัญชีเจ้าหนี้',
  `adjustment_date` date NOT NULL COMMENT 'วันที่ปรับปรุง',
  `adjustment_type` enum('credit','debit') NOT NULL COMMENT 'ประเภทการปรับปรุง (เพิ่ม/ลดหนี้)',
  `adjustment_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินที่ปรับปรุง',
  `reason` text NOT NULL COMMENT 'เหตุผลการปรับปรุง',
  `reference_document` varchar(100) DEFAULT NULL COMMENT 'เอกสารอ้างอิง',
  `created_by` int(11) NOT NULL COMMENT 'ผู้สร้าง',
  `approved_by` int(11) DEFAULT NULL COMMENT 'ผู้อนุมัติ',
  `approved_at` datetime DEFAULT NULL COMMENT 'วันที่อนุมัติ',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_account_payable_id` (`account_payable_id`),
  KEY `idx_adjustment_date` (`adjustment_date`),
  UNIQUE KEY `unique_adjustment_number` (`adjustment_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางสำหรับรับวางบิล
CREATE TABLE `bill_acceptances` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bill_number` varchar(50) NOT NULL COMMENT 'เลขที่ใบรับวางบิล',
  `supplier_id` int(11) NOT NULL COMMENT 'รหัสเจ้าหนี้/ผู้ขาย',
  `bill_date` date NOT NULL COMMENT 'วันที่รับบิล',
  `invoice_number` varchar(100) DEFAULT NULL COMMENT 'เลขที่ใบแจ้งหนี้จากผู้ขาย',
  `invoice_date` date DEFAULT NULL COMMENT 'วันที่ใบแจ้งหนี้',
  `due_date` date NOT NULL COMMENT 'วันที่ครบกำหนดชำระ',
  `subtotal` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'ยอดรวมก่อนภาษี',
  `vat_rate` decimal(5,2) NOT NULL DEFAULT 7.00 COMMENT 'อัตราภาษีมูลค่าเพิ่ม',
  `vat_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนภาษีมูลค่าเพิ่ม',
  `total_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินรวม',
  `withholding_tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'อัตราภาษีหัก ณ ที่จ่าย',
  `withholding_tax` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'ภาษีหัก ณ ที่จ่าย',
  `net_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินสุทธิ',
  `status` enum('pending','approved','rejected','converted') NOT NULL DEFAULT 'pending' COMMENT 'สถานะ',
  `description` text DEFAULT NULL COMMENT 'หมายเหตุ',
  `created_by` int(11) NOT NULL COMMENT 'ผู้สร้าง',
  `approved_by` int(11) DEFAULT NULL COMMENT 'ผู้อนุมัติ',
  `approved_at` datetime DEFAULT NULL COMMENT 'วันที่อนุมัติ',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_status` (`status`),
  KEY `idx_bill_date` (`bill_date`),
  UNIQUE KEY `unique_bill_number` (`bill_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางสำหรับรายการสินค้าในใบรับวางบิล
CREATE TABLE `bill_acceptance_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bill_acceptance_id` int(11) NOT NULL COMMENT 'รหัสใบรับวางบิล',
  `product_code` varchar(50) DEFAULT NULL COMMENT 'รหัสสินค้า',
  `product_name` varchar(255) NOT NULL COMMENT 'ชื่อสินค้า/บริการ',
  `description` text DEFAULT NULL COMMENT 'รายละเอียด',
  `quantity` decimal(10,3) NOT NULL DEFAULT 1.000 COMMENT 'จำนวน',
  `unit` varchar(20) DEFAULT 'ชิ้น' COMMENT 'หน่วยนับ',
  `unit_price` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'ราคาต่อหน่วย',
  `amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงิน',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_bill_acceptance_id` (`bill_acceptance_id`),
  KEY `idx_product_code` (`product_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางสำหรับเก็บเลขที่เอกสารอัตโนมัติ
INSERT INTO `running_numbers` (`prefix`, `current_number`, `format`, `description`) VALUES
('AP', 0, 'AP{YYYY}{MM}{DD}{####}', 'Account Payable Number'),
('PM', 0, 'PM{YYYY}{MM}{DD}{####}', 'Payment Number'),
('ADJ', 0, 'ADJ{YYYY}{MM}{DD}{####}', 'Adjustment Number'),
('BILL', 0, 'BILL{YYYY}{MM}{DD}{####}', 'Bill Acceptance Number')
ON DUPLICATE KEY UPDATE `prefix` = VALUES(`prefix`);
