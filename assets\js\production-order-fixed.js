const { createApp, ref, nextTick, onMounted } = Vue;

createApp({
    setup() {
        const products = ref([]);
        const processes = ref([]);
        const searchQuery = ref('');
        const selectAll = ref(false);
        const subTotal = ref(0);
        const vat = ref(0);
        const grandTotal = ref(0);
        const profitPercentage = ref(0);
        const costItems = ref([]);
        const documentDueDate = ref('');

        // Search products function
        const searchProducts = () => {
            if (!searchQuery.value.trim()) {
                // If search is empty, fetch all products
                fetchProductsByReservation();
                return;
            }
            
            const query = searchQuery.value.toLowerCase();
            const filteredProducts = products.value.filter(product => 
                product.product_code.toLowerCase().includes(query) ||
                product.product_name.toLowerCase().includes(query)
            );
            products.value = filteredProducts;
        };

        // Handle keyboard events manually to avoid Vue key modifier issues
        const handleKeyup = (event) => {
            if (event.key === 'Enter' || event.keyCode === 13) {
                searchProducts();
            }
        };

        // Toggle select all functionality
        const toggleSelectAll = () => {
            products.value.forEach(product => {
                product.selected = selectAll.value;
            });
        };

        // Update select all state
        const updateSelectAll = () => {
            const selectedCount = products.value.filter(p => p.selected).length;
            selectAll.value = selectedCount === products.value.length && products.value.length > 0;
        };

        const addCostItem = () => {
            costItems.value.push({
                id: 0,
                process_id: '',
                process_code: '',
                process_name: '',
                process_time: 0,
                price: 0,
                total: 0
            });
            
            // Initialize select2 for the new row after DOM update
            reinitializeSelect2();
        };

        const removeCostItem = (index) => {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    costItems.value.splice(index, 1);
                    Swal.fire(
                        'Deleted!',
                        'Your item has been deleted.',
                        'success'
                    )
                }
            })
        };

        // คำนวณราคารวม item.total
        const calculateTotal = (item) => {
            let total = item.price * item.process_time;
            return total;
        };

        // Handle process selection change
        const selectProcess = (item) => {
            const selectedProcess = processes.value.find(p => p.id == item.process_id);
            if (selectedProcess) {
                item.process_code = selectedProcess.process_code;
                item.process_name = selectedProcess.process_name;
                item.price = selectedProcess.process_price || 0;
            }
        };

        // Initialize select2 for new rows
        const initializeNewSelect2 = () => {
            nextTick(() => {
                $('.select2[data-vue-controlled]:not(.select2-hidden-accessible)').each(function() {
                    const $select = $(this);
                    const rowIndex = $select.closest('tr').index();
                    
                    // Initialize select2
                    $select.select2({
                        theme: 'bootstrap-5',
                        placeholder: 'เลือกข้อมูล'
                    });
                    
                    // Handle change event - update Vue model
                    $select.on('change', function() {
                        const value = $(this).val();
                        if (costItems.value[rowIndex]) {
                            costItems.value[rowIndex].process_id = value;
                            selectProcess(costItems.value[rowIndex]);
                        }
                    });
                    
                    // Set initial value if exists
                    if (costItems.value[rowIndex] && costItems.value[rowIndex].process_id) {
                        $select.val(costItems.value[rowIndex].process_id).trigger('change.select2');
                    }
                });
            });
        };

        // Watch for changes in costItems to reinitialize select2
        const reinitializeSelect2 = () => {
            nextTick(() => {
                // Destroy existing select2 instances
                $('.select2[data-vue-controlled].select2-hidden-accessible').each(function() {
                    $(this).select2('destroy');
                });
                
                // Reinitialize
                initializeNewSelect2();
            });
        };

        // Fetch processes data from the server
        const fetchProcesses = () => {
            const urlGetProcesses = $base_url + '/api/process.php';
            
            $.get(urlGetProcesses, function(data) {
                processes.value = data;
                // Initialize select2 for existing elements after data is loaded
                reinitializeSelect2();
            }, 'json').fail(function(_, textStatus, errorThrown) {
                console.error('Error fetching processes:', errorThrown);
            });
        };

        // คำนวณราคารวมหมด (Sub Total)
        const calculateSubTotal = () => {
            let total = 0;
            for (let i = 0; i < costItems.value.length; i++) {
                total += calculateTotal(costItems.value[i]);
            }
            subTotal.value = total;
            return subTotal.value;
        };

        // คำนวณยอดรวม price - Sub_Total 
        const calculateGrandTotal = () => {
            const sub = calculateSubTotal();
            const price = parseFloat($('#price').val()) || 0;
            const difference = price - sub;
            vat.value = 0;
            grandTotal.value = difference;
            
            // คำนวณ profit percentage ด้วยทุกครั้งที่มีการเปลี่ยนแปลง
            calculateProfitPercentage();
            
            return grandTotal.value;
        };

        // คำนวนยอดกำไรคิดเป็น % [grandTotal / Sub_Total ] * 100
        const calculateProfitPercentage = () => {
            const subTotal = calculateSubTotal();
            if (subTotal === 0) {
                profitPercentage.value = 0;
                return 0;
            }
            const calculated = (grandTotal.value / subTotal) * 100;
            profitPercentage.value = calculated.toFixed(2);
            return profitPercentage.value;
        };

        const fetchProductsByReservation = () => {
            var reservation_id = $('#reservation_id').val();
            var urlGetProduct = $base_url + '/api/product.php?reservation_id=' + reservation_id;

            if (reservation_id > 0) {
                $.get(urlGetProduct, function(productData){
                    for (let i = 0; i < productData.length; i++) {
                        productData[i].selected = false;
                    }
                    products.value = productData;
                }, 'json').fail(function(_, textStatus, errorThrown) {
                    console.error("AJAX request failed:", textStatus, errorThrown);
                    Swal.fire({
                        title: 'Error',
                        text: 'Failed to connect to the server. Please check your connection and try again.',
                        icon: 'error'
                    });
                });
            }
        };

        // Fetch cost details
        const fetchCostDetails = () => {
            const costId = new URLSearchParams(window.location.search).get('id');
            if (!costId) return;

            fetch(`${$base_url}/api/cost-detail.php?cost_id=${costId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Fetched cost details:', data);
                    
                    if (Array.isArray(data)) {
                        costItems.value = data.map(item => ({
                            id: item.id || 0,
                            process_id: item.process_id || '',
                            process_code: item.process_code || '',
                            process_name: item.process_name || '',
                            process_time: parseFloat(item.time) || 0,
                            price: parseFloat(item.cost_price) || 0,
                            total: parseFloat(item.total) || 0
                        }));
                        
                        // Re-initialize select2 after data is loaded
                        nextTick(() => {
                            reinitializeSelect2();
                        });
                    }
                })
                .catch(error => {
                    console.error('Error fetching cost details:', error);
                    Swal.fire({
                        title: 'Error',
                        text: 'ไม่สามารถโหลดข้อมูลรายละเอียดต้นทุนได้',
                        icon: 'error'
                    });
                });
        };

        return {
            searchQuery,
            selectAll,
            subTotal,
            vat,
            grandTotal,
            profitPercentage,
            costItems,
            products,
            processes,
            documentDueDate,
            searchProducts,
            handleKeyup,
            toggleSelectAll,
            updateSelectAll,
            removeCostItem,
            calculateTotal,
            calculateSubTotal,
            calculateGrandTotal,
            calculateProfitPercentage,
            fetchProductsByReservation,
            fetchCostDetails,
            fetchProcesses,
            addCostItem,
            selectProcess,
            initializeNewSelect2,
            reinitializeSelect2
        }
    },
    
    mounted() {
        this.fetchProcesses();
        this.fetchCostDetails();
        this.fetchProductsByReservation();
        
        // Make the component instance globally accessible
        window.costApp = this;
    }
}).mount('#appvue');

//## jquery section ##
$(function(){
    $('#customer_id').on('change', function(){
        var customer_id = $(this).val();
        var urlGetCustomer = $base_url + '/api/customer.php?id=' + customer_id;
        var urlReservation = $base_url + '/api/reservation.php?customer_id=' + customer_id;

        let customer;
        $.get(urlGetCustomer, function(customer) {
            $('#short_name').val(customer.short_name);
            $('#customer_name').val(customer.fullname);
            $('#contact_name').val(customer.contact_name);

           // Calculate due date again when reservation is selected
           var document_date = $('#document_date').val();
           var credit_day = $('#credit_day').val();

           if (document_date && credit_day) {
               // Convert Thai date format (DD/MM/YYYY) to Date object
               const [day, month, year] = document_date.split('/');
               const dateObj = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

               // Add credit days
               dateObj.setDate(dateObj.getDate() + parseInt(credit_day || 0));

               // Format for display (DD/MM/YYYY)
               const formattedDay = String(dateObj.getDate()).padStart(2, '0');
               const formattedMonth = String(dateObj.getMonth() + 1).padStart(2, '0');
               const formattedYear = dateObj.getFullYear();
               const formattedDueDate = `${formattedDay}/${formattedMonth}/${formattedYear}`;

               // Set the input value and update Vue app
               $('#document_due_date').val(formattedDueDate);

               // Update the Vue data
               const vueApp = document.getElementById('appvue').__vue_app__;
               if (vueApp && vueApp._instance) {
                   const vueInstance = vueApp._instance.proxy;
                   if (vueInstance && vueInstance.documentDueDate !== undefined) {
                       vueInstance.documentDueDate = formattedDueDate;
                   }
               }
           }

            // Fetch reservations after customer data is loaded
            $.get(urlReservation, function(reservations) {
                $('#reservation_id').empty();
                $('#reservation_id').append('<option value="">-- เลือกใบจอง PO --</option>');
                for (let i = 0; i < reservations.length; i++) {
                    $('#reservation_id').append('<option value="' + reservations[i].id + '">' + reservations[i].document_number + ' - ' + customer.short_name + '</option>');
                }
            }, 'json');
        }, 'json').catch((error) => {
            console.error('Error fetching data:', error);
        });           
    });

    // Fetch reservations after customer data is loaded
    $('#reservation_id').on('change', function() {
        var reservation_id = $(this).val();
        var urlGetProduct = $base_url + '/api/product.php?reservation_id=' + reservation_id;
        
        if (reservation_id > 0) {
            $.get(urlGetProduct, function(products) {
                // Clear and initialize product dropdown
                $('#product_id').empty();
                $('#product_id').append('<option value="">-- เลือกสินค้า --</option>');
                // Add products to dropdown
                products.forEach(function(product) {
                    $('#product_id').append(`<option value="${product.id}">${product.product_code} - ${product.product_name}</option>`);
                });
            }, 'json');
        } else {
            // Clear product dropdown if no reservation selected
            $('#product_id').empty();
            $('#product_id').append('<option value="">-- เลือกสินค้า --</option>');
        }
    });

    //เมื่อ reservation_id จะ ข้อมูล purchase_order มาแสดงใน
    $('#reservation_id').on('change', function() {
        var reservation_id = $(this).val();
        var urlGetReservation = $base_url + '/api/reservation.php?id=' + reservation_id;
        $.get(urlGetReservation, function(reservations) {
            if (reservations.length > 0) {
                $('#purchase_order').val(reservations[0].purchase_order);
                
                // แปลงวันที่จาก database format (YYYY-MM-DD) เป็น Thai format (DD/MM/YYYY)
                if (reservations[0].delivery_date) {
                    const deliveryDate = new Date(reservations[0].delivery_date);
                    const formattedDay = String(deliveryDate.getDate()).padStart(2, '0');
                    const formattedMonth = String(deliveryDate.getMonth() + 1).padStart(2, '0');
                    const formattedYear = deliveryDate.getFullYear();
                    const formattedDeliveryDate = `${formattedDay}/${formattedMonth}/${formattedYear}`;
                    $('#delivery_date').val(formattedDeliveryDate);
                } else {
                    $('#delivery_date').val('');
                }
                
                $('#document_no').val(reservations[0].quotation_document_no);
                $('#quotation_name').val(reservations[0].quotation_name);
                
                // Handle profile_image - show only PDF files
                if (reservations[0].profile_image) {
                    const profileImage = reservations[0].profile_image;
                    const fileExtension = profileImage.toLowerCase().split('.').pop();
                    
                    if (fileExtension === 'pdf') {
                        // Replace with PDF link for PDF files
                        $('#reservation_pdf_display').html(`<a href="${$base_url}/upload_image/product/${profileImage}" target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-file-pdf"></i> ดูไฟล์ PDF
                        </a>`);
                    } else {
                        // Clear if not PDF
                        $('#reservation_pdf_display').html(`<span class="text-muted">ไม่มีไฟล์ PDF</span>`);
                    }
                } else {
                    // Clear if no file
                    $('#reservation_pdf_display').html(`<span class="text-muted">ไม่มีไฟล์ PDF</span>`);
                }
            } else {
                $('#purchase_order').val(''); 
                $('#delivery_date').val(''); 
                $('#reservation_pdf_display').html(`<span class="text-muted">ไม่มีไฟล์ PDF</span>`); 
                $('#document_no').val(''); 
                $('#quotation_name').val(''); 
            }
        }, 'json').catch((error) => {
            console.error('Error fetching reservation data:', error);
            $('#purchase_order').val(''); 
            $('#delivery_date').val(''); 
            $('#quotation_name').val(''); 
            Swal.fire({
                title: 'Error',
                text: 'ไม่สามารถโหลดข้อมูลใบจองได้',
                icon: 'error'
            });
        });
    });

    // เมื่อกดปุ่มลบในรายการ จะแจ้งข้อความ
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        let urlDelete = $(this).attr('href');
        Swal.fire({
            text: "ยืนยันการลบข้อมูล?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'ลบข้อมูล!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = urlDelete;
            }
        });
    });

    // เมื่อเลือกสินค้าใน dropdown จะเพิ่มข้อมูลสินค้า > ชื่อสินค้า, ราคา, หน่วย
    $('#product_id').on('change', function() {
        var productId = $(this).val();
        var reservationId = $('#reservation_id').val();

        if (productId && reservationId) {
            console.log(`Fetching product details for Product ID: ${productId}, Reservation ID: ${reservationId}`);
            $.get(`${$base_url}/api/reservation-detail-for-cost.php?product_id=${productId}&reservation_id=${reservationId}`, function(product) {
                $('#product_name').val(product.product_name || '');
                $('#price').val(product.price || '');
                $('#quantity').val(product.quantity || '');
                $('#unit_name').val(product.unit_name || '');
                $('#materials_name').val(product.materials_name || '');
                $('#hardness_name').val(product.hardness_name || '');
                
                // Handle profile_image - show only PDF files
                if (product.profile_image) {
                    const profileImage = product.profile_image;
                    const fileExtension = profileImage.toLowerCase().split('.').pop();                    
                    if (fileExtension === 'pdf') {
                        // Replace with PDF link for PDF files
                        $('#product_pdf_display').html(`<a href="${$base_url}/upload_image/product/${profileImage}" target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-file-pdf"></i> ดูไฟล์ PDF
                        </a>`);
                    } else {
                        // Clear if not PDF
                        $('#product_pdf_display').html(`<span class="text-muted">ไม่มีไฟล์ PDF</span>`);
                    }
                } else {
                    // Clear if no file
                    $('#product_pdf_display').html(`<span class="text-muted">ไม่มีไฟล์ PDF</span>`); 
                }
            }, 'json');
        } else {            
            $('#product_name').val('');
            $('#price').val('');
            $('#quantity').val('');
            $('#unit_name').val('');
            $('#materials_name').val('');
            $('#hardness_name').val('');
            $('#product_pdf_display').html(`<span class="text-muted">ไม่มีไฟล์ PDF</span>`); 
        }
    });
});
