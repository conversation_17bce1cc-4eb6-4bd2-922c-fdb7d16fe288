const { createApp, ref } = Vue
const app = createApp({
    setup() {
        const receiptItems = ref([]);
        const bills = ref([]);
        const subTotal = ref(0);
        const vat = ref(0);
        const afterDiscount = ref(0);
        const grandTotal = ref(0);
        const withholding = ref(0);
        const grandTotalwith = ref(0);


        // คำนวณราคารวม item.sub_total sum sub_total
        const calculateSubTotal = () => {
            let total = 0;
            for (let i = 0; i < receiptItems.value.length; i++) {
                total += parseFloat(receiptItems.value[i].sub_total) || 0;
            }
            subTotal.value = total;
            return subTotal.value;
        }

        // คำนวณราคารวม item.vat_amount sum vat_amount 
        const calculateVatAmount = () => {
            let total = 0;
            for (let i = 0; i < receiptItems.value.length; i++) {
                total += parseFloat(receiptItems.value[i].vat_amount) || 0;
            }
            vat.value = total;
            return vat.value;
        }

        // คำนวณราคารวม item.withholding_amount sum withholding_amount 
        const calculateWithholding = () => {
            let total = 0;
            for (let i = 0; i < receiptItems.value.length; i++) {
                total += parseFloat(receiptItems.value[i].withholding_amount) || 0;
            }
            withholding.value = total;
            return withholding.value;
        }

        // คำนวณราคารวม item.grand_total sum grand_total 
        const calculateGrandTotal = () => {
            let total = 0;
            for (let i = 0; i < receiptItems.value.length; i++) {
                total += parseFloat(receiptItems.value[i].grand_total) || 0;
            }
            grandTotal.value = total;
            return grandTotal.value;
        }

        // คำนวณราคารวม item.grand_total_with sum grand_total_with
        const calculateGrandTotalWith = () => {
            let total = 0;
            for (let i = 0; i < receiptItems.value.length; i++) {
                total += parseFloat(receiptItems.value[i].grand_total_with) || 0;
            }
            grandTotalwith.value = total;
            return grandTotalwith.value;
        }

        // เลือกค้าจาก modal
        const onSelectBill = () => {
            const selectedbills = bills.value.filter(bill => bill.selected);
            
            selectedbills.forEach(bill => {
                const receiptItem = {
                    bill_id: bill.id,
                    volume_number: bill.volume_number,
                    document_number: bill.document_number,
                    document_date: bill.document_date,
                    document_due_date: bill.document_due_date,
                    purchase_order: bill.purchase_order,
                    sub_total: parseFloat(bill.sub_total) || 0,
                    vat_amount: parseFloat(bill.vat_amount) || 0,
                    grand_total: parseFloat(bill.grand_total) || 0,
                    withholding_amount: parseFloat(bill.withholding_amount) || 0,
                    grand_total_with: parseFloat(bill.grand_total_with) || 0,
                };
                
                const isDuplicate = receiptItems.value.some(item => item.bill_id === receiptItem.bill_id);
                if (!isDuplicate) {
                    receiptItems.value.push(receiptItem);
                }
            });

            // <|im_start|> Modal
            const modal = document.getElementById('billModal');
            const bootstrapModal = bootstrap.Modal.getInstance(modal);
            bootstrapModal.hide();

            // 3เซ็ตการ3
            bills.value.forEach(bill => bill.selected = false);
        };

        const removeReceiptItem = (index) => {
            receiptItems.value.splice(index, 1);
        };        const fetchIvoicesByCustomer = (customer_id) => {
            const urlGetBill = $base_url + '/api/bill.php?customer_id=' + customer_id;
            $.get(urlGetBill, function(data) {
                let billsData = JSON.parse(data);
                // Check if billsData is an array and not null/undefined
                if (Array.isArray(billsData)) {
                    for (let i = 0; i < billsData.length; i++) {
                        billsData[i].selected = false;
                    }
                    bills.value = billsData;
                } else {
                    // If no data or invalid data, set empty array
                    bills.value = [];
                }
            }).fail(function() {
                // Handle API error by setting empty array
                bills.value = [];
            });
        };
        // Define fetchReceiptDetails function
        const fetchReceiptDetails = () => {
            const urlParams = new URLSearchParams(window.location.search);
            const receiptId = urlParams.get('id');

            if(!receiptId) return;

            const urlGetReceiptDetails = $base_url + '/api/receipt-detail.php?receipt_id=' + receiptId;
            $.get(urlGetReceiptDetails, function(data) {
                const details = JSON.parse(data);                
                receiptItems.value = [];
                for (let i = 0; i < details.length; i++) {
                    const item = details[i];
                    receiptItems.value.push({
                        bill_id: item.bill_id,
                        volume_number: item.volume_number,
                        document_number: item.document_number,
                        document_date: item.document_date,
                        credit_days: item.credit_days,
                        document_due_date: item.document_due_date,
                        purchase_order: item.purchase_order,
                        sub_total: parseFloat(item.sub_total) || 0,
                        vat_amount: parseFloat(item.vat_amount) || 0,
                        grand_total: parseFloat(item.grand_total) || 0,
                        withholding_amount: parseFloat(item.withholding_amount) || 0,
                        grand_total_with: parseFloat(item.grand_total_with) || 0
                    });
                }
                
                calculateSubTotal();
                calculateVatAmount();
                calculateGrandTotal();
                calculateWithholding();
                calculateGrandTotalWith();
            });
        }


        return {
            receiptItems,
            bills,
            subTotal,
            vat,
            afterDiscount,
            grandTotal,
            withholding,
            calculateSubTotal,
            calculateVatAmount,
            calculateWithholding,
            calculateGrandTotal,
            calculateGrandTotalWith,
            onSelectBill,
            fetchReceiptDetails,
            removeReceiptItem,
            fetchIvoicesByCustomer,
            
        }
    },    mounted() {
        const customerId = $('#customer_id').find(':selected').val();        
        if (customerId && customerId > 0) {
            this.fetchIvoicesByCustomer(customerId);
        } else {
            // Initialize with empty bills array if no customer selected
            this.bills = [];
        }
    },
    created() {
        this.fetchReceiptDetails(); // Call fetchReceiptDetails when the component is created
        }
}).mount('#appvue');

//## jQuery Section ##
$(function () {
    // Handle customer_id change event
    $('#customer_id').on('change', function () {
        const customer_id = $(this).val();
        const urlGetCustomer = $base_url + '/api/customer.php?id=' + customer_id;
        const urlGetBill = $base_url + '/api/bill.php?customer_id=' + customer_id;
        
        // Fetch customer details        
        let customer = null; // Declare customer in a higher scope        
        $.get(urlGetCustomer, function (data) {            
            customer = JSON.parse(data); // Assign customer data to the higher scoped variable            
            const vatType = customer.vat_type == 1 ? 'Vat 7%' : 'Vat 0%';            
            $('#short_name').val(customer.short_name);            
            $('#customer_name').val(customer.fullname);            
            $('#contact_name').val(customer.contact_name);            
            $('#credit_day').val(customer.credit_day);            
            $('#payment_type').val(customer.payment_type);            
            $('#vat_type').val(customer.vat_type);            
            $('#vat_type_name').val(vatType);            
        });        
        // Fetch bills for the selected customer        
        $.get(urlGetBill, function (data) {            
            const bills = JSON.parse(data);            
            $('#bill_id').empty();            
            $('#bill_id').append('<option value="">-- เลือกใบวางบิล --</option>');            
            
            // Check if bills is an array and has data            
            if (Array.isArray(bills) && bills.length > 0) {                
                bills.forEach((bill) => {                
                    if (customer) { // Ensure customer is defined before using it                
                        $('#bill_id').append('<option value="' + bill.id + '">' + bill.document_number + ' - ' + customer.short_name + '</option>');                
                    }                
                });                
            }                
        }).fail(function() {            
            // Handle API error            
            $('#bill_id').empty();            
            $('#bill_id').append('<option value="">-- เลือกใบวางบิล --</option>');            
        });        
        // Handle bill_id change event        
        $('#bill_id').on('change', function () {            
            const bill_id = $(this).val();            
            const urlGetBillDetails = $base_url + '/api/bill-detail.php?bill_id=' + bill_id;            
            if (bill_id > 0) {
                $.get(urlGetBillDetails, function (data) {                
                    const billDetails = JSON.parse(data);                
                    if (Array.isArray(billDetails)) {                
                        billDetails.forEach((detail) => {                
                            detail.selected = false;                
                        });                
                        app.bills = billDetails;                
                    } else {                
                        app.bills = [];                
                    }                
                }).fail(function() {                
                    // Handle API error                
                    app.bills = [];                
                });                
            } else {                
                app.bills = [];                
            }                
        });        
    });        
    // Handle delete button click event
    $('.btn-delete').on('click', function(e) {        
        e.preventDefault();        
        const urlDelete = $(this).attr('href');
        Swal.fire({            
            text: "การลบข้อมูล?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'ลบข้อมูล!'
        }).then((result) => {            
            if (result.isConfirmed) {                
                window.location.href = urlDelete;                
            }            
        });        
    });        
});