// Customer Reservations Chart JavaScript
class CustomerReservationsChart {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.chart = null;
        this.apiUrl = 'api/customer-reservations-chart.php';
    }    async fetchData() {
        try {
            console.log('Fetching data from:', this.apiUrl); // Debug log
            const response = await fetch(this.apiUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            console.log('API Response:', data); // Debug log
            if (!data.success) {
                throw new Error(data.error || 'API returned error');
            }
            return data;
        } catch (error) {
            console.error('Error fetching customer reservations data:', error);
            throw error;
        }
    }

    formatCurrency(amount) {
        return '฿' + new Intl.NumberFormat('th-TH', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }

    // Generate colors for the chart
    generateColors(count) {
        const colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#4ECDC4', '#C9CBCF', '#45B7D1', '#96CEB4',
            '#FF9F40', '#4ECDC4', '#C9CBCF', '#45B7D1', '#96CEB4',
            '#D4A5A5', '#9B59B6', '#3498DB', '#E74C3C', '#2ECC71',
            '#F1C40F', '#1ABC9C', '#34495E', '#7F8C8D', '#E67E22'
        ];
        
        // If we need more colors than available, generate random colors
        while (colors.length < count) {
            const randomColor = `hsl(${Math.floor(Math.random() * 360)}, 70%, 60%)`;
            colors.push(randomColor);
        }
        
        return colors.slice(0, count);
    }    async createChart() {
        try {
            const data = await this.fetchData();
            
            console.log('Customer Reservations Data:', data); // Debug log
            
            if (!data.data || data.data.length === 0) {
                this.showNoDataMessage();
                return;
            }

            // Prepare chart data
            const labels = data.data.map(item => item.short_name || item.customer_name);
            const amounts = data.data.map(item => item.total_amount);
            const percentages = data.data.map(item => item.percentage);
            const colors = this.generateColors(data.data.length);

            // Destroy existing chart if it exists
            if (this.chart) {
                this.chart.destroy();
            }

            this.chart = new Chart(this.ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: amounts,
                        backgroundColor: colors,
                        borderColor: colors.map(color => color),
                        borderWidth: 2,
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                font: {
                                    size: 12
                                },
                                usePointStyle: true,
                                generateLabels: (chart) => {
                                    const dataset = chart.data.datasets[0];
                                    return chart.data.labels.map((label, index) => {
                                        const percentage = percentages[index];
                                        return {
                                            text: `${label} (${percentage}%)`,
                                            fillStyle: dataset.backgroundColor[index],
                                            strokeStyle: dataset.borderColor[index],
                                            lineWidth: dataset.borderWidth,
                                            hidden: false,
                                            index: index
                                        };
                                    });
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: (context) => {
                                    const label = context.label || '';
                                    const value = context.parsed;
                                    const percentage = percentages[context.dataIndex];
                                    const reservationCount = data.data[context.dataIndex].reservation_count;
                                    
                                    return [
                                        `${label}`,
                                        `ยอดรับจอง: ${this.formatCurrency(value)}`,
                                        `จำนวนใบ: ${reservationCount} ใบ`,
                                        `สัดส่วน: ${percentage}%`
                                    ];
                                }
                            }
                        }
                    },
                    cutout: '50%',
                    animation: {
                        animateRotate: true,
                        animateScale: true,
                        duration: 1000
                    }
                }
            });

            // Update summary information
            this.updateSummaryInfo(data);

        } catch (error) {
            console.error('Error creating customer reservations chart:', error);
            this.showError('ไม่สามารถโหลดข้อมูลกราฟได้ กรุณาลองใหม่อีกครั้ง');
        }
    }

    updateSummaryInfo(data) {
        // You can add summary info updates here if needed
        console.log('Customer Reservations Summary:', data.summary);
    }

    showNoDataMessage() {
        const chartContainer = this.canvas.parentElement;
        chartContainer.innerHTML = `
            <div class="alert alert-info text-center" role="alert">
                <i class="fas fa-info-circle"></i> ไม่มีข้อมูลการรับจองในเดือนนี้
            </div>
        `;
    }

    showError(message) {
        const chartContainer = this.canvas.parentElement;
        chartContainer.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle"></i> ${message}
            </div>
        `;
    }

    refresh() {
        this.createChart();
    }
}

// Initialize chart when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('customerReservationsChart')) {
        const customerReservationsChart = new CustomerReservationsChart('customerReservationsChart');
        customerReservationsChart.createChart();
        
        // Refresh chart every 5 minutes
        setInterval(() => {
            customerReservationsChart.refresh();
        }, 300000);
    }
});

// Export for global access
window.CustomerReservationsChart = CustomerReservationsChart;
