const { createApp, ref } = Vue;

const app = createApp({
    setup() {
        // Reactive data
        const products = ref([]);
        const materialOrderItems = ref([]);
        const currentPage = ref(1);
        const itemsPerPage = ref(10);
        const totalProducts = ref(0);
        const totalPages = ref(0);
        const selectAll = ref(false);
        const searchQuery = ref('');

        // Product selection handler
        const onSelectProduct = () => {
            const selectedProducts = products.value.filter(product => product.selected);
            
            selectedProducts.forEach(product => {
                const existingItem = materialOrderItems.value.find(item => item.product_id === product.id);
                
                if (!existingItem) {
                    materialOrderItems.value.push({
                        product_id: product.id,
                        product_code: product.product_code,
                        product_name: product.product_name,
                        quantity: product.quantity || 1,
                        unit_name: product.unit_name,
                        size: product.size || '',
                        country: product.country || '',
                        material_id: product.material_id || '',
                        material_name: product.material_name || '',
                        price_order: product.price_order || product.price || 0,
                        pdf: product.profile_image ? `${$base_url}/upload_image/product/${product.profile_image}` : null
                    });
                }
            });

            $('#productModal').modal('hide');
            resetProductSelection();
            initializeSelect2();
        };

        // Remove item from order
        const removeMaterialOrderItem = (index) => {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    materialOrderItems.value.splice(index, 1);
                    Swal.fire('Deleted!', 'Your item has been deleted.', 'success');
                }
            });
        };

        // Fetch products by customer
        const fetchProductsByCustomer = (customer_id, page = 1, search = '') => {
            if (!customer_id || customer_id == 0) return;

            currentPage.value = page;
            const reservation_id = $('#reservation_id').val() || '';
            const offset = (page - 1) * itemsPerPage.value;
            
            const baseUrl = `${$base_url}/api/product-material-order.php?customer_id=${customer_id}&reservation_id=${reservation_id}&limit=${itemsPerPage.value}&offset=${offset}`;
            const countUrl = `${$base_url}/api/product-material-order.php?customer_id=${customer_id}&reservation_id=${reservation_id}&count_only=1`;
            
            let productUrl = baseUrl;
            let productCountUrl = countUrl;
            
            if (search && search.trim() !== '') {
                const searchParam = `&search=${encodeURIComponent(search)}`;
                productUrl += searchParam;
                productCountUrl += searchParam;
            }

            // Get total count
            $.get(productCountUrl)
                .done(data => {
                    try {
                        const countResult = JSON.parse(data);
                        totalProducts.value = countResult.total;
                        totalPages.value = Math.ceil(totalProducts.value / itemsPerPage.value);
                    } catch (e) {
                        console.error("Error parsing count JSON:", e);
                    }
                })
                .fail(() => console.error("Failed to fetch product count"));

            // Get products
            $.get(productUrl)
                .done(data => {
                    try {
                        const productsData = JSON.parse(data);
                        products.value = productsData.map(product => ({
                            ...product,
                            selected: false,
                            quantity: product.quantity || 1
                        }));
                        selectAll.value = false;
                    } catch (e) {
                        console.error("Error parsing products JSON:", e);
                        showError('Failed to load product data. Please try again.');
                    }
                })
                .fail(() => {
                    showError('Failed to connect to the server. Please check your connection and try again.');
                });
        };

        // Fetch material order details (for edit mode)
        const fetchMaterialOrderDetails = () => {
            const materialOrderId = new URLSearchParams(window.location.search).get('id');
            if (!materialOrderId) return;

            fetch(`${$base_url}/api/material-order-detail.php?material_order_id=${materialOrderId}`)
                .then(response => {
                    if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    materialOrderItems.value = data.map(item => ({
                        ...item,
                        pdf: item.profile_image ? `${$base_url}/upload_image/product/${item.profile_image}` : null
                    }));
                    
                    setTimeout(() => initializeSelect2(), 100);
                })
                .catch(error => {
                    console.error('Error fetching material order details:', error);
                    showError('Failed to load material order details. Please try again.');
                });
        };

        // Search products
        const searchProducts = (event) => {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            const customerId = $('#customer_id').val();
            if (customerId > 0) {
                fetchProductsByCustomer(customerId, 1, searchQuery.value);
            }
        };

        // Pagination handlers
        const goToPage = (page, event) => {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            if (page >= 1 && page <= totalPages.value) {
                const customerId = $('#customer_id').val();
                if (customerId > 0) {
                    fetchProductsByCustomer(customerId, page, searchQuery.value);
                }
            }
        };

        const prevPage = (event) => {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            if (currentPage.value > 1) {
                goToPage(currentPage.value - 1, event);
            }
        };

        const nextPage = (event) => {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            if (currentPage.value < totalPages.value) {
                goToPage(currentPage.value + 1, event);
            }
        };

        // Select all functionality
        const toggleSelectAll = () => {
            products.value.forEach(product => {
                product.selected = selectAll.value;
            });
        };

        const updateSelectAll = () => {
            const selectedCount = products.value.filter(product => product.selected).length;
            const totalCount = products.value.length;
            selectAll.value = selectedCount === totalCount && totalCount > 0;
        };

        // Helper functions
        const resetProductSelection = () => {
            products.value.forEach(product => {
                product.selected = false;
            });
            selectAll.value = false;
        };

        const showError = (message) => {
            Swal.fire({
                title: 'Error',
                text: message,
                icon: 'error'
            });
        };

        return {
            // Data
            materialOrderItems,
            products,
            currentPage,
            itemsPerPage,
            totalProducts,
            totalPages,
            selectAll,
            searchQuery,
            
            // Methods
            onSelectProduct,
            removeMaterialOrderItem,
            fetchProductsByCustomer,
            fetchMaterialOrderDetails,
            searchProducts,
            goToPage,
            prevPage,
            nextPage,
            toggleSelectAll,
            updateSelectAll
        };
    },

    updated() {
        this.$nextTick(() => {
            initializeSelect2();
        });
    },

    mounted() {
        this.fetchMaterialOrderDetails();
        
        const customerId = $('#customer_id').val();
        if (customerId && customerId != '0') {
            this.fetchProductsByCustomer(customerId, 1, '');
        }
    },

    created() {
        this.fetchMaterialOrderDetails();
    }
}).mount('#appvue');

// Initialize Select2
function initializeSelect2() {
    $('.select2[data-vue-controlled]').each(function() {
        const $select = $(this);
        
        if ($select.hasClass('select2-hidden-accessible')) {
            $select.select2('destroy');
        }
        
        $select.select2({
            theme: 'bootstrap-5',
            width: '100%',
            placeholder: $(this).find('option:first').text() || 'เลือกข้อมูล',
            allowClear: true,
            dropdownAutoWidth: true,
            language: {
                noResults: function() {
                    return "ไม่พบข้อมูล";
                }
            }
        });

        $select.on('select2:open', function() {
            const searchField = document.querySelector('.select2-search__field');
            if (searchField) searchField.focus();
        });
    });
}

// jQuery handlers
$(function() {
    // Customer selection handler
    $('#customer_id').on('change', function() {
        const customer_id = $(this).val();
        
        if (!customer_id || customer_id == '0') {
            clearCustomerData();
            return;
        }

        loadCustomerData(customer_id);
        loadReservations(customer_id);
    });

    // Reservation selection handler
    $('#reservation_id').on('change', function() {
        const reservation_id = $(this).val();
        const customer_id = $('#customer_id').val();

        if (reservation_id && customer_id && customer_id != '0') {
            app.fetchProductsByCustomer(customer_id, 1, '');
        }
    });

    // Delete confirmation handler
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        const urlDelete = $(this).attr('href');
        
        Swal.fire({
            text: "การลบข้อมูล?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'ลบข้อมูล!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = urlDelete;
            }
        });
    });

    // Helper functions
    function loadCustomerData(customer_id) {
        const urlGetCustomer = `${$base_url}/api/customer.php?id=${customer_id}`;
        
        $.get(urlGetCustomer)
            .done(data => {
                try {
                    const customer = JSON.parse(data);
                    const vatType = customer.vat_type == 1 ? 'Vat 7%' : 'Vat 0%';
                    
                    $('#short_name').val(customer.short_name);
                    $('#customer_name').val(customer.fullname);
                    $('#contact_name').val(customer.contact_name);
                    $('#credit_day').val(customer.credit_day);
                    $('#payment_type').val(customer.payment_type);
                    $('#vat_type').val(customer.vat_type);
                    $('#vat_type_name').val(vatType);
                } catch (e) {
                    console.error("Error parsing customer JSON:", e);
                    showCustomerError();
                }
            })
            .fail(() => showCustomerError());
    }

    function loadReservations(customer_id) {
        const urlReservation = `${$base_url}/api/reservation.php?customer_id=${customer_id}`;
        
        $.get(urlReservation)
            .done(data => {
                try {
                    const reservations = JSON.parse(data);
                    populateReservationDropdown(reservations);
                } catch (e) {
                    console.error("Error parsing reservations JSON:", e);
                }
            })
            .fail(() => console.error('Failed to fetch reservations'));
    }

    function populateReservationDropdown(reservations) {
        const $reservationSelect = $('#reservation_id');
        $reservationSelect.empty().append('<option value="">-- เลือกใบรับจอง PO --</option>');
        
        reservations.forEach(reservation => {
            $reservationSelect.append(
                `<option value="${reservation.id}">${reservation.document_number}</option>`
            );
        });
        
        $reservationSelect.trigger('change.select2');
    }

    function clearCustomerData() {
        $('#short_name, #customer_name, #contact_name, #credit_day, #payment_type, #vat_type, #vat_type_name').val('');
        $('#reservation_id').empty().append('<option value="">-- เลือกใบรับจอง PO --</option>').trigger('change.select2');
        app.products = [];
    }

    function showCustomerError() {
        Swal.fire({
            title: 'Error',
            text: 'Failed to load customer data. Please try again.',
            icon: 'error'
        });
    }
});

// Document ready handlers
$(document).ready(function() {
    initializeSelect2();
    
    $('#productModal').on('shown.bs.modal', function() {
        $(this).removeAttr('aria-hidden');
    });

    // Prevent form submission in modal
    $(document).on('keydown', '#productModal input[type="text"]', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    });

    $(document).on('click', '#productModal .pagination button, #productModal .btn-outline-secondary', function(e) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    });
});
