// Sales Chart JavaScript
class SalesChart {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.chart = null;
        this.apiUrl = 'api/index-sales-garf.php';
    }

    async fetchData() {
        try {
            const response = await fetch(this.apiUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            if (!data.success) {
                throw new Error(data.error || 'API returned error');
            }
            return data;
        } catch (error) {
            console.error('Error fetching data:', error);
            throw error;
        }
    }

    formatCurrency(amount) {
        return '฿' + new Intl.NumberFormat('th-TH', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }

    formatNumber(number) {
        return new Intl.NumberFormat('th-TH').format(number);
    }

    async createChart() {
        try {
            const data = await this.fetchData();
              const labels = data.monthly_data.map(item => item.month);
            const currentYearSales = data.monthly_data.map(item => item.total_sales);
            const currentYearReservations = data.monthly_data.map(item => item.total_reservations);
            const previousYearData = data.previous_year_data;

            // Destroy existing chart if it exists
            if (this.chart) {
                this.chart.destroy();
            }

            this.chart = new Chart(this.ctx, {
                type: 'bar',
                data: {
                    labels: labels,                    datasets: [
                        {
                            label: `ยอดขาย ปี ${data.current_year}`,
                            data: currentYearSales,
                            backgroundColor: 'rgba(54, 162, 235, 0.8)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1,
                            borderRadius: 4,
                            borderSkipped: false,
                        },
                        {
                            label: `ยอดรับจองสินค้า ปี ${data.current_year}`,
                            data: currentYearReservations,
                            backgroundColor: 'rgba(255, 99, 132, 0.8)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1,
                            borderRadius: 4,
                            borderSkipped: false,
                        },
                        {
                            label: `ยอดขาย ปี ${data.previous_year}`,
                            data: previousYearData,
                            backgroundColor: 'rgba(201, 203, 207, 0.8)',
                            borderColor: 'rgba(201, 203, 207, 1)',
                            borderWidth: 1,
                            borderRadius: 4,
                            borderSkipped: false,
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'กราฟยอดขายและยอดรับจองสินค้ารายเดือน',
                            font: {
                                size: 18,
                                weight: 'bold'
                            },
                            padding: 20
                        },
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                padding: 20,
                                font: {
                                    size: 14
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: (context) => {
                                    const value = context.parsed.y;
                                    return `${context.dataset.label}: ${this.formatCurrency(value)}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: (value) => {
                                    return this.formatCurrency(value);
                                },
                                font: {
                                    size: 12
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 12
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });

            // Update summary statistics
            this.updateSummaryStats(data);

        } catch (error) {
            console.error('Error creating chart:', error);
            this.showError('ไม่สามารถโหลดข้อมูลกราฟได้ กรุณาลองใหม่อีกครั้ง');
        }
    }    updateSummaryStats(data) {
        const currentMonth = new Date().getMonth();
        const currentMonthData = data.monthly_data[currentMonth];
        const totalSalesThisYear = data.monthly_data.reduce((sum, month) => sum + month.total_sales, 0);
        const totalOrdersThisYear = data.monthly_data.reduce((sum, month) => sum + month.total_orders, 0);
        const totalReservationsThisYear = data.monthly_data.reduce((sum, month) => sum + month.total_reservations, 0);

        // Update summary elements if they exist
        const summaryElements = {
            'current-month-sales': this.formatCurrency(currentMonthData.total_sales),
            'current-month-orders': this.formatNumber(currentMonthData.total_orders),
            'total-year-sales': this.formatCurrency(totalSalesThisYear),
            'total-year-orders': this.formatNumber(totalOrdersThisYear),
            'current-month-reservations': this.formatCurrency(currentMonthData.total_reservations),
            'total-year-reservations': this.formatCurrency(totalReservationsThisYear)
        };

        Object.entries(summaryElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    showError(message) {
        const chartContainer = this.canvas.parentElement;
        chartContainer.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle"></i> ${message}
            </div>
        `;
    }

    refresh() {
        this.createChart();
    }
}

// Initialize chart when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('salesChart')) {
        const salesChart = new SalesChart('salesChart');
        salesChart.createChart();
        
        // Refresh chart every 5 minutes
        setInterval(() => {
            salesChart.refresh();
        }, 300000);
    }
});

// Export for global access
window.SalesChart = SalesChart;