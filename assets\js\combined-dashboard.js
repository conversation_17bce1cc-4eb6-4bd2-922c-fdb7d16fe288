// Combined Sales and Purchases Dashboard Manager
class CombinedDashboardManager {
    constructor() {
        this.salesApiUrl = 'api/index-sales-garf.php';
        this.purchasesApiUrl = 'api/index-garf.php';
        this.charts = {};
        this.salesData = null;
        this.purchasesData = null;
    }

    async fetchData() {
        try {
            // Fetch both sales and purchases data
            const [salesResponse, purchasesResponse] = await Promise.all([
                fetch(this.salesApiUrl),
                fetch(this.purchasesApiUrl)
            ]);
            
            if (!salesResponse.ok || !purchasesResponse.ok) {
                throw new Error('HTTP error!');
            }
            
            const [salesData, purchasesData] = await Promise.all([
                salesResponse.json(),
                purchasesResponse.json()
            ]);
            
            if (!salesData.success || !purchasesData.success) {
                throw new Error('API returned error');
            }
            
            this.salesData = salesData;
            this.purchasesData = purchasesData;
            
            return { salesData, purchasesData };
        } catch (error) {
            console.error('Error fetching data:', error);
            this.showError('ไม่สามารถโหลดข้อมูลได้ กรุณาลองใหม่อีกครั้ง - ข้อมูลจะไม่รวม invoices ที่ถูกยกเลิก');
            throw error;
        }
    }

    formatCurrency(amount) {
        return '฿' + new Intl.NumberFormat('th-TH', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }

    formatNumber(number) {
        return new Intl.NumberFormat('th-TH').format(number);
    }

    async initializeDashboard() {
        try {
            await this.fetchData();
            
            this.createSalesVsPurchasesChart();
            this.createMonthlyComparisonChart();
            this.createCombinedPieChart();
            this.updateSummaryStats();
            
        } catch (error) {
            console.error('Error initializing dashboard:', error);
            this.showError('ไม่สามารถโหลดข้อมูลได้ กรุณาลองใหม่อีกครั้ง');
        }
    }

    createSalesVsPurchasesChart() {
        const canvas = document.getElementById('salesVsPurchasesChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const labels = this.salesData.monthly_data.map(item => item.month);
          // Sales data (invoices only)
        const salesData = this.salesData.monthly_data.map(item => 
            item.total_sales
        );
        
        // Purchases data (combined total from purchases API)
        const purchasesData = this.purchasesData.monthly_data.map(item => 
            item.combined_total
        );

        this.charts.salesVsPurchases = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: `ยอดขาย ปี ${this.salesData.current_year}`,
                        data: salesData,
                        backgroundColor: 'rgba(40, 167, 69, 0.8)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 1
                    },
                    {
                        label: `ยอดซื้อ ปี ${this.purchasesData.current_year}`,
                        data: purchasesData,
                        backgroundColor: 'rgba(220, 53, 69, 0.8)',
                        borderColor: 'rgba(220, 53, 69, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                maintainAspectRatio: false,
                responsive: true,
                plugins: {
                    legend: {
                        display: true
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                return `${context.dataset.label}: ${this.formatCurrency(context.parsed.y)}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: (value) => this.formatCurrency(value)
                        }
                    }
                }
            }
        });
    }

    createMonthlyComparisonChart() {
        const canvas = document.getElementById('monthlyComparisonChart');
        if (!canvas) return;        const ctx = canvas.getContext('2d');
        const labels = this.salesData.monthly_data.map(item => item.month);
        
        // Sales line
        const salesData = this.salesData.monthly_data.map(item => 
            item.total_sales
        );
        
        // Purchases line
        const purchasesData = this.purchasesData.monthly_data.map(item => 
            item.combined_total
        );

        this.charts.monthlyComparison = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: `ยอดขาย ปี ${this.salesData.current_year}`,
                        data: salesData,
                        backgroundColor: 'rgba(40, 167, 69, 0.3)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        pointRadius: 4,
                        fill: false
                    },
                    {
                        label: `ยอดซื้อ ปี ${this.purchasesData.current_year}`,
                        data: purchasesData,
                        backgroundColor: 'rgba(220, 53, 69, 0.3)',
                        borderColor: 'rgba(220, 53, 69, 1)',
                        pointRadius: 4,
                        fill: false
                    }
                ]
            },
            options: {
                maintainAspectRatio: false,
                responsive: true,
                plugins: {
                    legend: {
                        display: true
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                return `${context.dataset.label}: ${this.formatCurrency(context.parsed.y)}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: (value) => this.formatCurrency(value)
                        }
                    }
                }
            }
        });
    }

    createCombinedPieChart() {
        const canvas = document.getElementById('combinedPieChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
          // Calculate totals
        const totalSales = this.salesData.monthly_data.reduce((sum, month) => 
            sum + month.total_sales, 0
        );
        
        const totalPurchases = this.purchasesData.monthly_data.reduce((sum, month) => 
            sum + month.combined_total, 0
        );
        
        const netProfit = totalSales - totalPurchases;

        this.charts.combinedPie = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['ยอดขาย', 'ยอดซื้อ', 'กำไรสุทธิ'],
                datasets: [{
                    data: [totalSales, totalPurchases, Math.max(0, netProfit)],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(220, 53, 69, 0.8)',
                        'rgba(0, 123, 255, 0.8)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(220, 53, 69, 1)',
                        'rgba(0, 123, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                responsive: true,
                plugins: {
                    legend: {
                        display: true
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                return `${context.label}: ${this.formatCurrency(context.parsed)}`;
                            }
                        }
                    }
                }
            }
        });
    }

    updateSummaryStats() {        // Calculate totals
        const totalSalesThisYear = this.salesData.monthly_data.reduce((sum, month) => 
            sum + month.total_sales, 0
        );
        
        const totalPurchasesThisYear = this.purchasesData.monthly_data.reduce((sum, month) => 
            sum + month.combined_total, 0
        );
        
        const netProfitThisYear = totalSalesThisYear - totalPurchasesThisYear;
          // Current month data
        const currentMonth = new Date().getMonth();
        const currentMonthSales = this.salesData.monthly_data[currentMonth].total_sales;
        const currentMonthPurchases = this.purchasesData.monthly_data[currentMonth].combined_total;
        const currentMonthProfit = currentMonthSales - currentMonthPurchases;

        // Update summary elements if they exist
        const summaryElements = {
            'total-sales-year': this.formatCurrency(totalSalesThisYear),
            'total-purchases-year': this.formatCurrency(totalPurchasesThisYear),
            'net-profit-year': this.formatCurrency(netProfitThisYear),
            'current-month-sales': this.formatCurrency(currentMonthSales),
            'current-month-purchases': this.formatCurrency(currentMonthPurchases),
            'current-month-profit': this.formatCurrency(currentMonthProfit)
        };

        Object.entries(summaryElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });

        // Update profit margin if elements exist
        const profitMargin = totalSalesThisYear > 0 ? (netProfitThisYear / totalSalesThisYear * 100) : 0;
        const profitMarginElement = document.getElementById('profit-margin');
        if (profitMarginElement) {
            profitMarginElement.textContent = `${profitMargin.toFixed(1)}%`;
        }
    }

    showError(message) {
        console.error(message);
        // Display error message on dashboard
        const errorContainer = document.getElementById('dashboard-error');
        if (errorContainer) {
            errorContainer.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> ${message}
                </div>
            `;
        }
    }

    refresh() {
        // Destroy all existing charts
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.destroy();
            }
        });
        this.charts = {};
        
        // Reinitialize dashboard
        this.initializeDashboard();
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const dashboardManager = new CombinedDashboardManager();
    dashboardManager.initializeDashboard();
    
    // Refresh dashboard every 5 minutes
    setInterval(() => {
        dashboardManager.refresh();
    }, 300000);
    
    // Make it globally accessible
    window.dashboardManager = dashboardManager;
});

// Export for global access
window.CombinedDashboardManager = CombinedDashboardManager;
