-- สคีมาสำหรับระบบบัญชีเจ้าหนี้ (Account Payable System)



-- ตารางสำหรับการปรับปรุงหนี้ (Credit/Debit Memo)
CREATE TABLE `account_payable_adjustments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `adjustment_number` varchar(50) NOT NULL COMMENT 'เลขที่การปรับปรุง',
  `account_payable_id` int(11) NOT NULL COMMENT 'รหัสบัญชีเจ้าหนี้',
  `adjustment_date` date NOT NULL COMMENT 'วันที่ปรับปรุง',
  `adjustment_type` enum('credit','debit') NOT NULL COMMENT 'ประเภทการปรับปรุง (เพิ่ม/ลดหนี้)',
  `adjustment_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินที่ปรับปรุง',
  `reason` text NOT NULL COMMENT 'เหตุผลการปรับปรุง',
  `reference_document` varchar(100) DEFAULT NULL COMMENT 'เอกสารอ้างอิง',
  `created_by` int(11) NOT NULL COMMENT 'ผู้สร้าง',
  `approved_by` int(11) DEFAULT NULL COMMENT 'ผู้อนุมัติ',
  `approved_at` datetime DEFAULT NULL COMMENT 'วันที่อนุมัติ',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  <PERSON><PERSON><PERSON> `idx_account_payable_id` (`account_payable_id`),
  <PERSON><PERSON>Y `idx_adjustment_date` (`adjustment_date`),
  UNIQUE KEY `unique_adjustment_number` (`adjustment_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


