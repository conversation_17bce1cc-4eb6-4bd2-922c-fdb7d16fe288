const { createApp, ref, nextTick } = Vue;
const app = createApp({    setup() {
        // ตัวแปรสำหรับข้อมูลหลัก - เริ่มต้นด้วย arrays ว่าง เพื่อป้องกัน null/undefined errors
        const products = ref([]);
        const suppliers = ref([]);
        const customers = ref([]);
        const reservations = ref([]);
        const materials = ref([]);
        const hardnesses = ref([]);
        const procurementItems = ref([]);
          // เพิ่มรายการสินค้าใหม่
        const addProcurementItem = () => {
            const newIndex = procurementItems.value.length;
            procurementItems.value.push({
                id: 0,
                customer_id: '',
                customer_code: '',
                customer_name: '',
                short_name: '',
                product_id: '',
                product_code: '',
                product_name: '',
                quantity: 0,
                unit_name: '',
                reservation_id: '',
                document_number: '',
                purchase_order_no: '',
                quotation_no: '',                
                material_id: '',
                materials_name: '',
                hardness_id: '',
                hardness_name: '',                note: '',
                pdf: null
            });
            
            // รอให้ DOM อัพเดตก่อนเรียก select2
            nextTick(() => {
                initializeSelect2ForRow(newIndex);
            });
        };// ลบรายการสินค้า
        const removeProcurementItem = (index) => {
            Swal.fire({
                title: 'คุณแน่ใจหรือไม่?',
                text: "คุณจะไม่สามารถกู้คืนข้อมูลนี้ได้!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'ใช่, ลบเลย!',
                cancelButtonText: 'ยกเลิก'
            }).then((result) => {
                if (result.isConfirmed) {
                    // ทำลาย select2 ก่อนลบ
                    destroySelect2ForRow(index);
                    procurementItems.value.splice(index, 1);
                    
                    // รีเซ็ตดัชนีสำหรับ select2 ของแถวที่เหลือ
                    // nextTick(() => {
                    //     reinitializeAllSelect2();
                    // });
                    
                    Swal.fire(
                        'ลบแล้ว!',
                        'รายการของคุณถูกลบแล้ว',
                        'success'
                    )
                }
            })
        };        
        // เลือกลูกค้า
        const selectCustomer = (item) => {
            if (!item) return; // ป้องกันการส่ง item ที่เป็น null/undefined
            
            // ค้นหาลูกค้าทั้งจาก id และ customer_code
            const selectedCustomer = customers.value.find(c => 
                c && (c.id == item.customer_id || c.customer_code == item.customer_id)
            );
            
            if (selectedCustomer) {
                item.short_name = selectedCustomer.short_name || '';
                item.customer_code = selectedCustomer.customer_code || '';
                item.customer_name = selectedCustomer.customer_name || selectedCustomer.fullname || '';
                
                console.log('Selected customer:', selectedCustomer);
                console.log('Fetching reservations for customer ID:', selectedCustomer.id);
                
                // โหลดใบรับจองเฉพาะลูกค้านั้น ดึงข้อมูล reservation_id และข้อมูลที่เกี่ยวข้องตาม customer_id มาแสดง
                fetchReservationsByCustomer(selectedCustomer.id, item);
            } else {
                // ล้างข้อมูลถ้าไม่เลือกลูกค้า
                item.short_name = '';
                item.customer_code = '';
                item.customer_name = '';
                item.reservation_id = '';
                item.document_number = '';
                item.purchase_order_no = '';
                item.quotation_no = '';
                clearProductData(item);
                
                // ล้างข้อมูลใบรับจองในแถวนี้
                clearReservationDropdown(item);
            }
        };        // เลือกสินค้า
        const selectProduct = (item) => {
            if (!item) return; // ป้องกันการส่ง item ที่เป็น null/undefined
            
            const selectedProduct = products.value.find(p => p.product_code == item.product_code);
            if (selectedProduct) {
                item.product_id = selectedProduct.id || '';
                item.product_name = selectedProduct.product_name || '';
                item.quantity = selectedProduct.quantity || 0;
                item.unit_name = selectedProduct.unit_name || '';
                item.material_id = selectedProduct.material_id || '';
                item.materials_name = selectedProduct.materials_name || '';
                item.hardness_id = selectedProduct.hardness_id || '';
                item.hardness_name = selectedProduct.hardness_name || '';
                item.pdf = selectedProduct.profile_image ? $base_url + '/upload_image/product/' + selectedProduct.profile_image : null;
            }
        };

        // เลือกแมททีเรียล
        const selectMaterial = (item) => {
            const selectedMaterial = materials.value.find(m => m.id == item.material_id);
            if (selectedMaterial) {
                item.materials_name = selectedMaterial.materials_name || '';
            }
        };

        // เลือกความแข็ง
        const selectHardness = (item) => {
            const selectedHardness = hardnesses.value.find(h => h.id == item.hardness_id);
            if (selectedHardness) {
                item.hardness_name = selectedHardness.hardness_name || '';
            }
        };        
        // ดึงข้อมูลผู้ขาย
        const fetchSuppliers = () => {
            const urlGetSuppliers = $base_url + '/api/get-supplier.php';
            
            $.get(urlGetSuppliers, function(data) {
                suppliers.value = Array.isArray(data) ? data : [];
                console.log('Suppliers loaded:', suppliers.value.length, 'items');
            }, 'json').fail(function(xhr, textStatus, errorThrown) {
                console.error('Error fetching suppliers:', textStatus, errorThrown);
                console.error('Response text:', xhr.responseText);
                suppliers.value = [];
            });
        };

        // ดึงข้อมูลลูกค้า
        const fetchCustomers = () => {
            const urlGetCustomers = $base_url + '/api/get-customer.php';
            
            $.get(urlGetCustomers, function(data) {
                customers.value = Array.isArray(data) ? data : [];
                console.log('Customers loaded:', customers.value.length, 'items');
            }, 'json').fail(function(xhr, textStatus, errorThrown) {
                console.error('Error fetching customers:', textStatus, errorThrown);
                console.error('Response text:', xhr.responseText);
                customers.value = [];
            });
        };        
        // เมื่อเลือกลูกค้า จะดึงข้อมูลใบรับจองของลูกค้า
        // ฟังก์ชันสำหรับดึงข้อมูลใบรับจองตาม customer_id
        const fetchReservationsByCustomer = (customerId, item) => {
            if (!customerId) return;
            
            console.log('Fetching reservations for customer ID:', customerId);
            const urlGetReservations = $base_url + '/api/get-reservation.php?customer_id=' + customerId;
            
            $.get(urlGetReservations, function(data) {
                const customerReservations = Array.isArray(data) ? data : [];
                console.log('Customer reservations loaded:', customerReservations.length, 'items');
                
                const itemIndex = procurementItems.value.findIndex(i => i === item);
                
                if (itemIndex !== -1) {
                    nextTick(() => {
                        const reservationSelector = `.reservation-select-${itemIndex}`;
                        $(reservationSelector).empty();
                        $(reservationSelector).append('<option value="">เลือกใบรับจอง</option>');
                          customerReservations.forEach(reservation => {
                            console.log('Adding reservation option - ID:', reservation.id, 'Document:', reservation.document_number);
                            $(reservationSelector).append(
                                `<option value="${reservation.id}" 
                                 data-po="${reservation.purchase_order_no || ''}"
                                 data-quotation="${reservation.quotation_no || ''}"
                                 data-document="${reservation.document_number || ''}"
                                 data-customer-short-name="${reservation.customer_short_name || ''}"
                                >${reservation.document_number} ${reservation.purchase_order_no ? '(PO: ' + reservation.purchase_order_no + ')' : ''}</option>`
                            );
                        });
                        
                        // รีเฟรช select2 หากมีการใช้งาน
                        if ($(reservationSelector).hasClass('select2-hidden-accessible')) {
                            $(reservationSelector).trigger('change');
                        }
                        
                        console.log('ดึงข้อมูลใบรับจองและข้อมูลที่เกี่ยวข้องตาม customer_id เสร็จแล้ว');
                    });
                }
            }, 'json').fail(function(xhr, textStatus, errorThrown) {
                console.error('Error fetching reservations by customer:', textStatus, errorThrown);
                console.error('Response text:', xhr.responseText);
                
                // แสดงข้อความแจ้งเตือนเมื่อเกิดข้อผิดพลาด
                Swal.fire({
                    title: 'เกิดข้อผิดพลาด',
                    text: 'ไม่สามารถดึงข้อมูลใบรับจองของลูกค้าได้',
                    icon: 'error',
                    timer: 3000
                });
            });
        };    

        // ดึงข้อมูลรายละเอียดใบจัดจ้าง
        const fetchProcurementDetails = () => {
            console.log('Fetching procurement details...');
            
            // ตรวจสอบข้อมูลจาก PHP ก่อน (จะเร็วกว่า)
            if (Array.isArray(window.procurementDetails) && window.procurementDetails.length > 0) {

                console.log('Loading procurement details from PHP data...');
                loadProcurementDetailsFromData(window.procurementDetails);
                return;
            }
            
            // หา procurement ID
            const procurementId = window.procurementId || document.getElementById('procurement_id')?.value || '';
            
            if (!procurementId) {
                console.log('No procurement ID found');
                return;
            }
            
            console.log('Loading procurement details from API for ID:', procurementId);
            
            const urlGetProcurementDetails = $base_url + '/api/procurement-details.php?procurement_id=' + procurementId;
            
            $.get(urlGetProcurementDetails, function(data) {
                console.log('Procurement details loaded from API:', data);
                
                if (Array.isArray(data) && data.length > 0) {
                    loadProcurementDetailsFromData(data);
                } else {
                    console.log('No procurement details found');
                }
            }, 'json').fail(function(xhr, textStatus, errorThrown) {
                console.error('Error fetching procurement details:', textStatus, errorThrown);
            });
        };        

        // โหลดใบรับจองสำหรับลูกค้าเฉพาะ (ใช้ในหน้าแก้ไข)
        const loadReservationsForCustomer = (customerId, rowIndex, selectedReservationId = null) => {
            if (!customerId) return;
            
            console.log(`Loading reservations for customer ${customerId} at row ${rowIndex}`);
            const urlGetReservations = $base_url + '/api/get-reservation.php?customer_id=' + customerId;
            
            $.get(urlGetReservations, function(data) {
                const customerReservations = Array.isArray(data) ? data : [];
                
                const reservationSelector = `.reservation-select-${rowIndex}`;
                $(reservationSelector).empty();
                $(reservationSelector).append('<option value="">เลือกใบรับจอง</option>');
                
                customerReservations.forEach(reservation => {
                    const isSelected = selectedReservationId && reservation.id == selectedReservationId ? 'selected' : '';
                    $(reservationSelector).append(
                        `<option value="${reservation.id}" 
                         data-po="${reservation.purchase_order_no || ''}"
                         data-quotation="${reservation.quotation_no || ''}"
                         data-document="${reservation.document_number || ''}"
                         ${isSelected}
                        >${reservation.document_number} ${reservation.purchase_order_no ? '(PO: ' + reservation.purchase_order_no + ')' : ''}</option>`
                    );
                });
                
                // รีเฟรช select2 และเซ็ตค่า
                if ($(reservationSelector).hasClass('select2-hidden-accessible')) {
                    $(reservationSelector).val(selectedReservationId).trigger('change');
                } else {
                    $(reservationSelector).val(selectedReservationId);
                }
                
            }, 'json').fail(function(xhr, textStatus, errorThrown) {
                console.error(`Error loading reservations for customer ${customerId}:`, textStatus, errorThrown);
            });
        };
        // ฟังก์ชันสำหรับโหลดข้อมูลจาก array ที่ได้มา
        const loadProcurementDetailsFromData = (data) => {
            console.log('Loading procurement details:', data.length, 'items');
            
            // ล้างข้อมูลเก่า
            procurementItems.value = [];
              // เพิ่มข้อมูลใหม่
            data.forEach((detail, index) => {
                // หาข้อมูลลูกค้าจาก customer_id เพื่อดึง short_name
                let customerShortName = detail.short_name || detail.customer_short_name || '';
                if (detail.customer_id && customers.value.length > 0) {
                    const customerData = customers.value.find(c => c.id == detail.customer_id);
                    if (customerData) {
                        customerShortName = customerData.short_name || '';
                    }
                }
                
                procurementItems.value.push({
                    id: detail.id || 0,
                    customer_id: detail.customer_id || detail.customer_code || '',
                    customer_code: detail.customer_code || '',
                    customer_name: detail.customer_name || detail.customer_short_name || '',
                    short_name: customerShortName,
                    product_id: detail.product_id || '',
                    product_code: detail.product_code || '',
                    product_name: detail.product_name || '',
                    quantity: detail.quantity || '',
                    unit_name: detail.unit_name || '',
                    reservation_id: detail.reservation_id || '',
                    document_number: detail.document_number || detail.reservation_document_number || '',
                    purchase_order_no: detail.purchase_order_no || '',
                    quotation_no: detail.quotation_no || '',                    
                    material_id: detail.material_id || '',
                    materials_name: detail.materials_name || '',
                    hardness_id: detail.hardness_id || detail.hardnes_id || '',
                    hardness_name: detail.hardness_name || detail.hardnes_name || '',                    note: detail.note || '',
                    pdf: detail.profile_image ? $base_url + '/upload_image/product/' + detail.profile_image : (detail.pdf || null)
                });
            });
            
            console.log('Loaded', procurementItems.value.length, 'procurement items');
            
            // รอให้ DOM อัพเดตแล้วเริ่มต้น select2
            nextTick(() => {
                initializeSelect2AndSetValues();
            });
        };
        
        // ฟังก์ชันรวมสำหรับเริ่มต้น select2 และตั้งค่า
        const initializeSelect2AndSetValues = () => {
            console.log('Initializing select2 for all rows...');
            
            procurementItems.value.forEach((item, index) => {
                // เริ่มต้น select2
                initializeSelect2ForRow(index);
                
                // ตั้งค่าทันทีหลังจากเริ่มต้น select2
                setTimeout(() => {
                    // ตั้งค่าลูกค้า
                    if (item.customer_id) {
                        $(`.customer-select-${index}`).val(item.customer_id).trigger('change');
                        
                        // โหลดใบรับจองสำหรับลูกค้านี้
                        setTimeout(() => {
                            loadReservationsForCustomer(item.customer_id, index, item.reservation_id);
                        }, 200);
                    }
                    
                    // ตั้งค่าสินค้า
                    if (item.product_code) {
                        setTimeout(() => {
                            $(`.product-select-${index}`).val(item.product_code).trigger('change');
                        }, 400);
                    }
                }, 100 * (index + 1)); // เว้นระยะเวลาตาม index เพื่อป้องกันการชนกัน
            });
            
            console.log('Select2 initialization completed');
        };        // ดึงข้อมูลสินค้าจากใบรับจอง (เรียกจากหน้าแก้ไขใบจัดจ้าง)
        const fetchReservationProducts = (reservationId, item) => {
            if (!reservationId) return;
            
            const urlGetReservationProducts = $base_url + '/api/get-product.php?reservation_id=' + reservationId + '&all_products=1';
            $.get(urlGetReservationProducts, function(data) {
                products.value = Array.isArray(data) ? data : [];
                
                // อัพเดต dropdown สินค้าหลัก
                $('#product_id').empty();
                $('#product_id').append('<option value="">-- เลือกรหัสสินค้า --</option>');
                
                if (Array.isArray(data)) {
                    data.forEach(function(product) {
                        $('#product_id').append(
                            '<option value="' + product.id + '" data-code="' + product.product_code + '" data-name="' + product.product_name + '">' + 
                            product.product_code + ' - ' + product.product_name + 
                            '</option>'
                        );
                    });
                }
            });
        };        
        // ฟังก์ชันสำหรับเลือกสินค้าจาก dropdown หลัก
        const selectMainProduct = () => {
            const productId = $('#product_id').val();
            const selectedOption = $('#product_id option:selected');
            const customerId = $('#customer_id').val();
            const reservationId = $('#reservation_id').val();
            
            // ตรวจสอบว่าเลือกข้อมูลครบหรือไม่
            if (!customerId || !reservationId || !productId) {
            let missingFields = [];
            if (!customerId) missingFields.push('ลูกค้า');
            if (!reservationId) missingFields.push('ใบรับจอง');
            if (!productId) missingFields.push('สินค้า');
            
            Swal.fire({
                title: 'กรุณาเลือกข้อมูล',
                text: 'กรุณาเลือก: ' + missingFields.join(', '),
                icon: 'warning'
            });
            return;
            }
            
            if (selectedOption.length > 0) {
            const productCode = selectedOption.data('code');
            const productName = selectedOption.data('name');
            
            // หาสินค้าที่เลือกในข้อมูล products
            const selectedProduct = products.value.find(p => p.id == productId);
            
            // หาข้อมูลลูกค้าที่เลือก
            const selectedCustomer = customers.value.find(c => c.id == customerId);
            
            // หาข้อมูลใบรับจองที่เลือก
            const selectedReservation = reservations.value.find(r => r.id == reservationId);
            
            if (selectedProduct) {
                // แสดงหน้าต่างยืนยันก่อนเพิ่มลงตาราง
                Swal.fire({
                title: 'ยืนยันการเพิ่มสินค้า',
                html: `<div class="text-start">
                    <strong>ลูกค้า:</strong> ${selectedCustomer ? selectedCustomer.short_name || selectedCustomer.customer_name || selectedCustomer.fullname : 'ไม่ระบุ'}<br>
                    <strong>ใบรับจอง:</strong> ${selectedReservation ? selectedReservation.document_number : 'ไม่ระบุ'}<br>
                    <strong>รหัสสินค้า:</strong> ${selectedProduct.product_code}<br>
                    <strong>ชื่อสินค้า:</strong> ${selectedProduct.product_name}
                </div>`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'เพิ่มสินค้า',
                cancelButtonText: 'ยกเลิก'
                }).then((result) => {
                if (result.isConfirmed) {
                    // เพิ่มสินค้าเข้าตาราง
                    addProcurementItem();
                    
                    // อัพเดตข้อมูลในแถวล่าสุด
                    const lastIndex = procurementItems.value.length - 1;
                    const lastItem = procurementItems.value[lastIndex];
                      // ข้อมูลสินค้า
                    lastItem.product_id = selectedProduct.id;
                    lastItem.product_code = selectedProduct.product_code;
                    lastItem.product_name = selectedProduct.product_name;
                    lastItem.quantity = selectedProduct.quantity;
                    lastItem.unit_name = selectedProduct.unit_name;
                    lastItem.materials_name = selectedProduct.materials_name;
                    lastItem.hardness_name = selectedProduct.hardness_name;
                    lastItem.material_id = selectedProduct.material_id;
                    lastItem.hardnes_id = selectedProduct.hardnes_id;
                    lastItem.pdf = selectedProduct.profile_image ? $base_url + '/upload_image/product/' + selectedProduct.profile_image : null;

                    // ข้อมูลลูกค้า
                    if (selectedCustomer) {
                    lastItem.customer_id = selectedCustomer.id;
                    lastItem.customer_code = selectedCustomer.customer_code || '';
                    lastItem.customer_name = selectedCustomer.short_name || selectedCustomer.customer_name || selectedCustomer.fullname || '';
                    lastItem.short_name = selectedCustomer.short_name || '';
                    }
                    
                    // ข้อมูลใบรับจอง
                    if (selectedReservation) {
                    lastItem.reservation_id = selectedReservation.id;
                    lastItem.document_number = selectedReservation.document_number || '';
                    lastItem.purchase_order_no = selectedReservation.purchase_order_no || selectedProduct.purchase_order_no || '';
                    lastItem.quotation_no = selectedReservation.quotation_no || selectedProduct.quotation_no || '';
                    }
                    
                    // ล้างการเลือกใน dropdown หลัก
                    $('#product_id').val('').trigger('change');
                    $('#customer_id').val('').trigger('change');
                    $('#reservation_id').val('').trigger('change');

                    console.log('Added product to table:', lastItem);
                    
                    Swal.fire({
                    title: 'สำเร็จ!',
                    text: 'เพิ่มสินค้าเรียบร้อยแล้ว',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                    });
                }
                });
            }
            }
        };

           // ล้างข้อมูลใบรับจองในแถว
        const clearReservationDropdown = (item) => {
            // หา index ของ item ปัจจุบัน
            const itemIndex = procurementItems.value.findIndex(i => i === item);
            
            if (itemIndex !== -1) {
                nextTick(() => {
                    const reservationSelector = `.reservation-select-${itemIndex}`;
                    
                    // ล้าง options เดิม
                    $(reservationSelector).empty();
                    $(reservationSelector).append('<option value="">เลือกใบรับจอง</option>');
                    
                    // รีเฟรช select2
                    if ($(reservationSelector).hasClass('select2-hidden-accessible')) {
                        $(reservationSelector).trigger('change');
                    }
                });
            }
        };        // ฟังก์ชันสำหรับจัดการ select2
        const initializeSelect2ForRow = (index) => {
            // เริ่มต้น select2 สำหรับลูกค้า
            const customerSelector = `.customer-select-${index}`;
            $(customerSelector).empty();
            $(customerSelector).append('<option value="">เลือกลูกค้า</option>');
            
            // เพิ่ม options ลูกค้า
            customers.value.forEach(customer => {
                $(customerSelector).append(
                    `<option value="${customer.id}">${customer.customer_code} - ${customer.short_name || customer.fullname}</option>`
                );
            });
            
            $(customerSelector).select2({
                theme: 'bootstrap-5',
                placeholder: 'เลือกลูกค้า',
                allowClear: true
            }).on('change', function(e) {
                const selectedValue = $(this).val();
                // อัพเดต Vue reactive data
                procurementItems.value[index].customer_id = selectedValue;
                // เรียก method ของ Vue เพื่อดึงข้อมูล reservation_id และข้อมูลที่เกี่ยวข้องตาม customer_id
                window.procurementApp.selectCustomer(procurementItems.value[index]);
            });

            // เริ่มต้น select2 สำหรับใบรับจอง
            const reservationSelector = `.reservation-select-${index}`;
            $(reservationSelector).empty();
            $(reservationSelector).append('<option value="">เลือกใบรับจอง</option>');
            
            $(reservationSelector).select2({
                theme: 'bootstrap-5',
                placeholder: 'เลือกใบรับจอง',
                allowClear: true
            }).on('change', function(e) {
                const selectedValue = $(this).val();
                const selectedOption = $(this).find(':selected');
                
                // อัพเดต Vue reactive data
                procurementItems.value[index].reservation_id = selectedValue;
                procurementItems.value[index].document_number = selectedOption.data('document') || '';
                procurementItems.value[index].purchase_order_no = selectedOption.data('po') || '';
                procurementItems.value[index].quotation_no = selectedOption.data('quotation') || '';

                fetchReservationProducts(procurementItems.value[index].reservation_id, procurementItems.value[index]);

            });

            // เริ่มต้น select2 สำหรับสินค้า
            const productSelector = `.product-select-${index}`;
            $(productSelector).empty();
            $(productSelector).append('<option value="">เลือกสินค้า</option>');
            
            // เพิ่ม options สินค้า
            products.value.forEach(product => {
                $(productSelector).append(
                    `<option value="${product.product_code}">${product.product_code}</option>`
                );
            });
            
            $(productSelector).select2({
                theme: 'bootstrap-5',
                placeholder: 'เลือกสินค้า',
                allowClear: true
            }).on('change', function(e) {
                const selectedValue = $(this).val();
                // อัพเดต Vue reactive data
                procurementItems.value[index].product_code = selectedValue;
                // เรียก method ของ Vue
                window.procurementApp.selectProduct(procurementItems.value[index]);
            });
        };const destroySelect2ForRow = (index) => {
            // ทำลาย select2 สำหรับลูกค้า
            const customerSelector = `.customer-select-${index}`;
            if ($(customerSelector).hasClass("select2-hidden-accessible")) {
                $(customerSelector).select2('destroy');
            }

            // ทำลาย select2 สำหรับใบรับจอง
            const reservationSelector = `.reservation-select-${index}`;
            if ($(reservationSelector).hasClass("select2-hidden-accessible")) {
                $(reservationSelector).select2('destroy');
            }

            // ทำลาย select2 สำหรับสินค้า
            const productSelector = `.product-select-${index}`;
            if ($(productSelector).hasClass("select2-hidden-accessible")) {
                $(productSelector).select2('destroy');
            }
        };        const reinitializeAllSelect2 = () => {
            // ทำลาย select2 ทั้งหมดก่อน
            $('.customer-select, .reservation-select, .product-select').each(function() {
                if ($(this).hasClass("select2-hidden-accessible")) {
                    $(this).select2('destroy');
                }
            });

            // เริ่มต้น select2 ใหม่สำหรับทุกแถว
            procurementItems.value.forEach((item, index) => {
                initializeSelect2ForRow(index);
            });
        };
        return {
            // ตัวแปร
            products,
            suppliers,
            customers,
            reservations,
            materials,
            hardnesses,
            procurementItems,
            
            // ฟังก์ชัน
            addProcurementItem,
            removeProcurementItem,
            selectCustomer,
            selectProduct,
            selectMaterial,
            selectHardness,             
            fetchSuppliers,
            fetchCustomers,          
            fetchProcurementDetails,
            loadProcurementDetailsFromData,
            initializeSelect2AndSetValues,
            fetchReservationsByCustomer,
            loadReservationsForCustomer,            
            fetchReservationProducts,
            selectMainProduct,
            clearReservationDropdown,
            initializeSelect2ForRow,
            destroySelect2ForRow,
            reinitializeAllSelect2
        }
    },
    mounted() {
        console.log('Vue component mounted');
            // ทำให้ component สามารถเข้าถึงได้จาก global scope
            window.procurementApp = this;
            
            // รอให้ DOM พร้อมก่อนโหลดข้อมูล
            this.$nextTick(() => {
                console.log('DOM is ready, loading data...');
                
                // โหลดข้อมูลพื้นฐาน
                this.fetchSuppliers();
                this.fetchCustomers();
                
                // รอให้ข้อมูลพื้นฐานโหลดเสร็จ แล้วค่อยโหลด procurement details
                setTimeout(() => {
                    this.fetchProcurementDetails();
                }, 500);
            });
        },
    created() {
    }
}).mount('#appvue');

// เมื่อเลือกผู้ขาย จะโหลดข้อมูลผู้ขาย
    $('#supplier_id').on('change', function(){
        var supplier_id = $(this).val();
        var urlGetSupplier = $base_url + '/api/get-supplier.php?id=' + supplier_id;

        if (supplier_id > 0) {            
            $.get(urlGetSupplier, function(supplier) {
                if (supplier && typeof supplier === 'object') {
                    $('#short_name').val(supplier.short_name || '');
                    $('#supplier_name').val(supplier.fullname || '');
                    $('#contact_name').val(supplier.contact_name || '');
                } else {
                    console.warn('Invalid supplier data received:', supplier);
                }
            }, 'json').fail(function(xhr, textStatus, errorThrown) {
                console.error('Error fetching supplier data:', textStatus, errorThrown);
                console.error('Response text:', xhr.responseText);
                Swal.fire({
                    title: 'เกิดข้อผิดพลาด',
                    text: 'ไม่สามารถโหลดข้อมูลผู้ขายได้',
                    icon: 'error'
                });
            });
        } else {
            // ล้างข้อมูลถ้าไม่เลือกผู้ขาย
            $('#short_name').val('');
            $('#supplier_name').val('');
            $('#contact_name').val('');
        }
    });

//## jQuery Section ##
$(function(){
    // เมื่อเลือกลูกค้า จะโหลดข้อมูลลูกค้าและใบรับจอง
    $('#customer_id').on('change', function(){
        var customer_id = $(this).val();
        var urlGetCustomer = $base_url + '/api/get-customer.php?id=' + customer_id;
        var urlReservation = $base_url + '/api/get-reservation.php?customer_id=' + customer_id;

        if (customer_id > 0) {            // โหลดข้อมูลลูกค้า
            $.get(urlGetCustomer, function(customer) {
                if (customer && typeof customer === 'object') {
                    // อัพเดตข้อมูลลูกค้าใน Vue.js ถ้าเป็น array (API บางตัวส่งมาเป็น array)
                    const customerData = Array.isArray(customer) ? customer[0] : customer;
                    
                    if (window.procurementApp && customerData) {
                        // อัพเดตข้อมูลลูกค้าใน Vue app
                        const existingCustomer = window.procurementApp.customers.find(c => c.id == customerData.id);
                        if (!existingCustomer) {
                            window.procurementApp.customers.push(customerData);
                        }
                    }
                    console.log('Customer data loaded:', customerData);
                } else {
                    console.warn('Invalid customer data received:', customer);
                }
            }, 'json').fail(function(xhr, textStatus, errorThrown) {
                console.error('Error fetching customer data:', textStatus, errorThrown);
                Swal.fire({
                    title: 'เกิดข้อผิดพลาด',
                    text: 'ไม่สามารถโหลดข้อมูลลูกค้าได้',
                    icon: 'error'
                });
            });// โหลดใบรับจองของลูกค้า
            $.get(urlReservation, function(reservations) {
                if (Array.isArray(reservations)) {
                    // อัพเดตข้อมูลใน Vue.js
                    if (window.procurementApp) {
                        window.procurementApp.reservations = reservations;
                    }
                    
                    $('#reservation_id').empty();
                    $('#reservation_id').append('<option value="">เลือกใบรับจอง</option>');
                    reservations.forEach(function(reservation) {
                        $('#reservation_id').append(
                            `<option value="${reservation.id}" 
                             data-po="${reservation.purchase_order_no || ''}"
                             data-quotation="${reservation.quotation_no || ''}"
                             data-document="${reservation.document_number || ''}"
                            >${reservation.document_number} ${reservation.purchase_order_no ? '(PO: ' + reservation.purchase_order_no + ')' : ''}</option>`
                        );
                    });
                    console.log('Reservations loaded:', reservations);
                } else {
                    console.warn('Invalid reservations data received:', reservations);
                }
            }, 'json').fail(function(xhr, textStatus, errorThrown) {
                console.error('Error fetching reservations:', textStatus, errorThrown);
                Swal.fire({
                    title: 'เกิดข้อผิดพลาด',
                    text: 'ไม่สามารถโหลดข้อมูลใบรับจองได้',
                    icon: 'error'
                });
            });
        } else {
            // ล้างข้อมูลถ้าไม่เลือกลูกค้า
            $('#reservation_id').empty();
            $('#reservation_id').append('<option value="">เลือกใบรับจอง</option>');
        }
    })    // เมื่อเลือกรายการใบรับจอง จะโหลดข้อมูลสินค้า
    $('#reservation_id').on('change', function(){
        var reservation_id = $(this).val();
        var urlGetProducts = $base_url + '/api/get-product.php?reservation_id=' + reservation_id + '&all_products=1';

        if (reservation_id > 0) {
            // โหลดข้อมูลสินค้า
            $.get(urlGetProducts, function(data) {
                if (Array.isArray(data)) {
                    // อัพเดต dropdown สินค้า
                    $('#product_id').empty();
                    $('#product_id').append('<option value="">-- เลือกรหัสสินค้า --</option>');
                    
                    data.forEach(function(product) {
                        $('#product_id').append(
                            '<option value="' + product.id + '" data-code="' + product.product_code + '" data-name="' + product.product_name + '">' + 
                            product.product_code + ' - ' + product.product_name + 
                            '</option>'
                        );
                    });
                    
                    // อัพเดตข้อมูลใน Vue.js
                    if (window.procurementApp) {
                        window.procurementApp.products = data;
                    }
                    console.log('Products data loaded:', data);
                    
                    // เปิดใช้งานปุ่ม "เลือกสินค้า" หลังจากโหลดข้อมูลสินค้าเสร็จ
                    $('#product_id').prop('disabled', false);
                } else {
                    console.warn('Invalid products data received:', data);
                }
            }, 'json').fail(function(xhr, textStatus, errorThrown) {
                console.error('Error fetching products:', textStatus, errorThrown);
                Swal.fire({
                    title: 'เกิดข้อผิดพลาด',
                    text: 'ไม่สามารถโหลดข้อมูลสินค้าได้',
                    icon: 'error'
                });
            });
        } else {
            // ล้างข้อมูลถ้าไม่เลือกรายการใบรับจอง
            $('#product_id').empty();
            $('#product_id').append('<option value="">-- เลือกรหัสสินค้า --</option>');
            
            if (window.procurementApp) {
                window.procurementApp.products = [];
            }
        }
    });

    // เมื่อกดปุ่มลบในรายการ จะแจ้งข้อความยืนยัน
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        let urlDelete = $(this).attr('href');
        Swal.fire({
            text: "ยืนยันการลบข้อมูล?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'ลบข้อมูล!',
            cancelButtonText: 'ยกเลิก'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = urlDelete;
            }
        });
    });

    // Initialize datepicker if available
    if ($.fn.datepicker) {
        $('.datepicker').datepicker({
            format: 'dd/mm/yyyy',
            autoclose: true,
            todayHighlight: true
        });
    }    // Initialize select2 if available
    if ($.fn.select2) {
        $('.select2').select2({
            theme: 'bootstrap-5',
            placeholder: 'เลือกข้อมูล'
        });
    }

    // เมื่อเลือกสินค้าจาก dropdown หลัก
    $('#product_id').on('change', function() {
        if (window.procurementApp && window.procurementApp.selectMainProduct) {
            window.procurementApp.selectMainProduct();
        }
    });
});
