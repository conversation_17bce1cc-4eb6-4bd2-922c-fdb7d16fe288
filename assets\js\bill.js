const { createApp, ref } = Vue
const app = createApp({
    setup() {
        const billItems = ref([]);
        const invoices = ref([]);
        const subTotal = ref(0);
        const vat = ref(0);
        const afterDiscount = ref(0);
        const grandTotal = ref(0);
        const withholding = ref(0);
        const grandTotalwith = ref(0);

        // เลือกค้าจาก modal
        const onSelectInvoice = () => {
            const selectedInvoices = invoices.value.filter(invoice => invoice.selected);
            
            selectedInvoices.forEach(invoice => {
                const billItem = {
                    invoice_id: invoice.id,
                    volume_number: invoice.volume_number,
                    document_number: invoice.document_number,
                    document_date: invoice.document_date,
                    document_due_date: invoice.document_due_date,
                    purchase_order: invoice.purchase_order,
                    sub_total: parseFloat(invoice.sub_total) || 0,
                    vat_amount: parseFloat(invoice.vat_amount) || 0,
                    grand_total: parseFloat(invoice.grand_total) || 0,
                    withholding_amount: parseFloat(invoice.withholding_amount) || 0,
                    grand_total_with: parseFloat(invoice.grand_total_with) || 0,
                    bill_id: invoice.bill_id,
                };
                
                const isDuplicate = billItems.value.some(item => item.invoice_id === billItem.invoice_id);
                if (!isDuplicate) {
                    billItems.value.push(billItem);
                }
                
            });
           

            // <|im_start|> Modal
            const modal = document.getElementById('invoiceModal');
            const bootstrapModal = bootstrap.Modal.getInstance(modal);
            bootstrapModal.hide();

            // 3เซ็ตการ3
            invoices.value.forEach(invoice => invoice.selected = false);
        };

        const removeBillItem = (index) => {
            billItems.value.splice(index, 1);
        };

        // คำนวณราคารวม item.sub_total sum sub_total
        const calculateSubTotal = () => {
            let total = 0;
            for (let i = 0; i < billItems.value.length; i++) {
                total += parseFloat(billItems.value[i].sub_total) || 0;
            }
            subTotal.value = total;
            return subTotal.value;
        }

        // คำนวณราคารวม item.vat_amount sum vat_amount 
        const calculateVatAmount = () => {
            let total = 0;
            for (let i = 0; i < billItems.value.length; i++) {
                total += parseFloat(billItems.value[i].vat_amount) || 0;
            }
            vat.value = total;
            return vat.value;
        }

        // คำนวณราคารวม item.withholding_amount sum withholding_amount 
        const calculateWithholding = () => {
            let total = 0;
            for (let i = 0; i < billItems.value.length; i++) {
                total += parseFloat(billItems.value[i].withholding_amount) || 0;
            }
            withholding.value = total;
            return withholding.value;
        }

        // คำนวณราคารวม item.grand_total sum grand_total 
        const calculateGrandTotal = () => {
            let total = 0;
            for (let i = 0; i < billItems.value.length; i++) {
                total += parseFloat(billItems.value[i].grand_total) || 0;
            }
            grandTotal.value = total;
            return grandTotal.value;
        }

        // คำนวณราคารวม item.grand_total_with sum grand_total_with
        const calculateGrandTotalWith = () => {
            let total = 0;
            for (let i = 0; i < billItems.value.length; i++) {
                total += parseFloat(billItems.value[i].grand_total_with) || 0;
            }
            grandTotalwith.value = total;
            return grandTotalwith.value;
        }

        
        

        const fetchIvoicesByCustomer = (customer_id) => {
            const urlGetInvoice = $base_url + '/api/invoice.php?customer_id=' + customer_id;
            $.get(urlGetInvoice, function(data) {
                let invoicesData = JSON.parse(data);
                for (let i = 0; i < invoicesData.length; i++) {
                    invoicesData[i].selected = false;
                }
                invoices.value = invoicesData;
            });
        };

        // Define fetchBillDetails function
        const fetchBillDetails = () => {
            const urlParams = new URLSearchParams(window.location.search);
            const bill_id = urlParams.get('id');
            const urlGetBillDetails = $base_url + '/api/bill-detail.php?bill_id=' + bill_id;
            $.get(urlGetBillDetails, function(data) {
                let billDetails = JSON.parse(data);
                billItems.value = billDetails.map(detail => ({
                    invoice_id: detail.invoice_id,
                    volume_number: detail.volume_number,
                    document_number: detail.document_number,
                    document_date: detail.document_date,
                    document_due_date: detail.document_due_date,
                    purchase_order: detail.purchase_order,
                    sub_total: parseFloat(detail.sub_total) || 0,
                    vat_amount: parseFloat(detail.vat_amount) || 0,
                    grand_total: parseFloat(detail.grand_total) || 0,
                    withholding_amount: parseFloat(detail.withholding_amount) || 0,
                    grand_total_with: parseFloat(detail.grand_total_with) || 0
                }));
            });
        };

        return {
            billItems,
            invoices,
            subTotal,
            vat,
            afterDiscount,
            grandTotal,
            withholding,
            calculateSubTotal,
            calculateVatAmount,
            calculateWithholding,
            calculateGrandTotal,
            calculateGrandTotalWith,
            onSelectInvoice,
            removeBillItem,
            fetchIvoicesByCustomer,
            fetchBillDetails // Ensure fetchBillDetails is included here
        }
    },
    mounted() {
        const customerId = $('#customer_id').find(':selected').val();        
        if (customerId > 0) {
            this.fetchIvoicesByCustomer(customerId);
        }
    },
    created() {
        this.fetchBillDetails(); // Call fetchBillDetails when the component is created
        }
        
}).mount('#appvue');

//## jquery section ##
$(function(){
    $('#customer_id').on('change', function(){
        var customer_id = $(this).val();        
        var urlGetCustomer = $base_url + '/api/customer.php?id=' + customer_id;
        var urlGetInvoice = $base_url + '/api/invoice.php?customer_id=' + customer_id;

        // get customer
        $.get(urlGetCustomer, function(data){
            let customer = JSON.parse(data);
            const vatType = customer.vat_type==1 ? 'Vat 7% (รวมภาษี)' : customer.vat_type==2 ? 'Vat 7% (แยกภาษี)' : 'Vat 0%';
            $('#short_name').val(customer.short_name);
            $('#customer_name').val(customer.fullname);
            $('#contact_name').val(customer.contact_name);
            $('#credit_day').val(customer.credit_day);
            $('#payment_type').val(customer.payment_type);
            $('#vat_type').val(customer.vat_type);
            $('#vat_type_name').val(vatType);
        });

        $.get(urlGetInvoice, function(data){
            let invoices = JSON.parse(data);
            for (let i = 0; i < invoices.length; i++) {
                invoices[i].selected = false;
            }

            app.invoices = invoices;
        });
    });   
    
    // เมื่อกดปุ่มลบในรายการ จะแจ้งข้อความ
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        let urlDelete = $(this).attr('href');
        Swal.fire({            
            text: "การลบข้อมูล?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'ลบข้อมูล!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = urlDelete;
            }
        });
    });
});
