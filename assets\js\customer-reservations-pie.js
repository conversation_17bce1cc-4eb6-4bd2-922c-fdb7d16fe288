// Customer Reservations Pie Chart JavaScript
class CustomerReservationsPieChart {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.chart = null;
        this.apiUrl = 'api/customer-reservations-pie.php';
    }

    async fetchData() {
        try {
            const response = await fetch(this.apiUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            if (!data.success) {
                throw new Error(data.error || 'API returned error');
            }
            return data;
        } catch (error) {
            console.error('Error fetching customer reservations data:', error);
            throw error;
        }
    }

    formatCurrency(amount) {
        return '฿' + new Intl.NumberFormat('th-TH', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }

    formatNumber(number) {
        return new Intl.NumberFormat('th-TH').format(number);
    }

    async createChart() {
        try {
            const data = await this.fetchData();

            if (!data.pie_data || data.pie_data.length === 0) {
                this.showNoData();
                return;
            }

            const labels = data.pie_data.map(item => item.label);
            const values = data.pie_data.map(item => item.value);
            const percentages = data.pie_data.map(item => item.percentage);
            const colors = data.pie_data.map(item => item.color);

            // Destroy existing chart if it exists
            if (this.chart) {
                this.chart.destroy();
            }

            this.chart = new Chart(this.ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: colors,
                        borderColor: colors.map(color => color.replace('0.8', '1')),
                        borderWidth: 2,
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `สัดส่วนการรับจอง PO ตามลูกค้า ปี ${data.current_year}`,
                            font: {
                                size: 16,
                                weight: 'bold'
                            },
                            padding: 20
                        },
                        legend: {
                            display: true,
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                font: {
                                    size: 12
                                },
                                usePointStyle: true,
                                generateLabels: (chart) => {
                                    const data = chart.data;
                                    if (data.labels.length && data.datasets.length) {
                                        return data.labels.map((label, index) => {
                                            const value = data.datasets[0].data[index];
                                            const percentage = percentages[index];
                                            return {
                                                text: `${label}: ${percentage}%`,
                                                fillStyle: data.datasets[0].backgroundColor[index],
                                                strokeStyle: data.datasets[0].borderColor[index],
                                                lineWidth: data.datasets[0].borderWidth,
                                                hidden: false,
                                                index: index
                                            };
                                        });
                                    }
                                    return [];
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: (context) => {
                                    const label = context.label || '';
                                    const value = context.parsed;
                                    const percentage = percentages[context.dataIndex];
                                    const orders = data.pie_data[context.dataIndex].orders_count;
                                    return [
                                        `${label}`,
                                        `มูลค่า: ${this.formatCurrency(value)}`,
                                        `สัดส่วน: ${percentage}%`,
                                        `จำนวนใบ: ${this.formatNumber(orders)} ใบ`
                                    ];
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false
                    }
                }
            });

            // Update summary info
            this.updateSummaryInfo(data);

        } catch (error) {
            console.error('Error creating customer reservations pie chart:', error);
            this.showError('ไม่สามารถโหลดข้อมูลสัดส่วนการรับจองตามลูกค้าได้ กรุณาลองใหม่อีกครั้ง');
        }
    }

    updateSummaryInfo(data) {
        // Create summary information below the chart
        const chartContainer = this.canvas.parentElement;
        let summaryDiv = chartContainer.querySelector('.pie-chart-summary');
        
        if (!summaryDiv) {
            summaryDiv = document.createElement('div');
            summaryDiv.className = 'pie-chart-summary mt-3';
            chartContainer.appendChild(summaryDiv);
        }

        summaryDiv.innerHTML = `
            <div class="row text-center">
                <div class="col-md-4">
                    <small class="text-muted">รวมทั้งหมด</small>
                    <h5 class="text-primary">${this.formatCurrency(data.total_amount)}</h5>
                </div>
                <div class="col-md-4">
                    <small class="text-muted">จำนวนใบ</small>
                    <h5 class="text-success">${this.formatNumber(data.total_orders)} ใบ</h5>
                </div>
                <div class="col-md-4">
                    <small class="text-muted">จำนวนลูกค้า</small>
                    <h5 class="text-info">${this.formatNumber(data.total_customers)} ราย</h5>
                </div>
            </div>
        `;
    }

    showNoData() {
        const chartContainer = this.canvas.parentElement;
        chartContainer.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">ไม่มีข้อมูลการรับจองในปีนี้</h5>
            </div>
        `;
    }

    showError(message) {
        const chartContainer = this.canvas.parentElement;
        chartContainer.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle"></i> ${message}
            </div>
        `;
    }

    refresh() {
        this.createChart();
    }
}

// Initialize pie chart when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('pieChart')) {
        const pieChart = new CustomerReservationsPieChart('pieChart');
        pieChart.createChart();
        
        // Refresh chart every 5 minutes
        setInterval(() => {
            pieChart.refresh();
        }, 300000);
    }
});

// Export for global access
window.CustomerReservationsPieChart = CustomerReservationsPieChart;
