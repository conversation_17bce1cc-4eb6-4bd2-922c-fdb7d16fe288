const { createApp, ref } = Vue
const app = createApp({
    setup() {        
        const quotationItems = ref([]);
        const products = ref([]);
        const subTotal = ref(0);
        const vat = ref(0); 
        const afterDiscount = ref(0);
        const grandTotal = ref(0);


        // เลือกสินค้าจาก modal
        const onSelectProduct = () => {
            for(let i = 0; i < products.value.length; i++) {
                if (products.value[i].selected) {
                    let qu = {
                        id: 0,
                        product_id: products.value[i].id,
                        product_code: products.value[i].product_code,
                        product_name: products.value[i].product_name,
                        quantity: products.value[i].quantity || 1, // Use the quantity from products or default to 1
                        unit_name: products.value[i].unit_name,
                        price: products.value[i].price,
                        discount: 0,
                        total: (products.value[i].quantity || 1) * products.value[i].price, // Calculate total based on quantity
                        pdf: products.value[i].profile_image ? $base_url + '/upload_image/product/' + products.value[i].profile_image : null,
                    };
                    quotationItems.value.push(qu);
                }
            }

            $('#productModal').modal('hide');            
            products.value.forEach(product => {
                product.selected = false;
            });
        };

        // ลบรายการสินค้าในใบเสนอราคา
        const removeQuotationItem = (index) => {
            Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
            if (result.isConfirmed) {
                quotationItems.value.splice(index, 1);
                Swal.fire(
                'Deleted!',
                'Your item has been deleted.',
                'success'
                )
            }
            })
        }

        // คำนวณราคารวม item.total
        const calculateTotal = (item) => {
            let total = item.price * item.quantity;
            if (item.discount > 0) {
                total -= item.discount;
            }
            return total;
        }

        // คำนวณราคารวมทั้งหมด
        const calculateSubTotal = () => {
            let total = 0;
            for (let i = 0; i < quotationItems.value.length; i++) {
                total += calculateTotal(quotationItems.value[i]);
            }
            subTotal.value = total;
            return subTotal.value;
        }

        // คำนวณยอดรวมสุทธิ
        const calculateGrandTotal = () => {
            const discount = parseFloat($('#discount').val()) || 0;
            const vatType = parseInt($('#vat_type').val());
            const subTotal = calculateSubTotal();
            afterDiscount.value = (subTotal - discount).toFixed(2);
                        
            if (vatType === 1) {
                vat.value = (afterDiscount.value * 0.07).toFixed(2);
            }
                     
            grandTotal.value = (parseFloat(afterDiscount.value) + parseFloat(vat.value)).toFixed(2);

            return grandTotal.value;
        }

        // Fetch quotation details on created
        const fetchQuotationDetails = () => {            
            const urlParams = new URLSearchParams(window.location.search);
            const quotationId = urlParams.get('id');

            if(!quotationId) return;

            const urlGetQuotationDetails = $base_url + '/api/quotation-detail.php?quotation_id=' + quotationId;
            $.get(urlGetQuotationDetails, function(data) {
                const details = JSON.parse(data);                
                quotationItems.value = [];
                for (let i = 0; i < details.length; i++) {
                    const item = details[i];
                    quotationItems.value.push({
                        id: item.id,
                        product_id: item.product_id,
                        product_code: item.product_code,
                        product_name: item.product_name,
                        quantity: item.quantity,
                        unit_name: item.unit_name,
                        price: item.price,
                        discount: item.discount,
                        total: item.total,
                        pdf: item.profile_image ? $base_url + '/upload_image/product/' + item.profile_image : null,
                    });
                }
                
                calculateGrandTotal();
            });
        };

        const fetchProductsByCustomer = (customer_id) => {
            const urlGetProduct = $base_url + '/api/product.php?customer_id=' + customer_id;
            $.get(urlGetProduct, function(data) {
                let productsData = JSON.parse(data);
                for (let i = 0; i < productsData.length; i++) {
                    productsData[i].selected = false;
                }
                products.value = productsData;
            });
        };

        return {
            subTotal,
            vat,
            afterDiscount,
            grandTotal,
            quotationItems,
            products,
            calculateGrandTotal,
            onSelectProduct,
            removeQuotationItem,
            calculateTotal,
            calculateSubTotal,
            fetchQuotationDetails,
            fetchProductsByCustomer
        }
    },
    mounted() {
        const customerId = $('#customer_id').find(':selected').val();        
        if (customerId > 0) {
            this.fetchProductsByCustomer(customerId);
        }
        this.fetchQuotationDetails();        
    }
}).mount('#appvue');

//## jquery section ##
$(function(){
    $('#customer_id').on('change', function(){
        var customer_id = $(this).val();        
        var urlGetCustomer = $base_url + '/api/customer.php?id=' + customer_id;
        var urlGetProduct = $base_url + '/api/product.php?customer_id=' + customer_id;

        // get customer
        $.get(urlGetCustomer, function(data){
            let customer = JSON.parse(data);
            const vatType = customer.vat_type==1 ? 'Vat 7%' : 'Vat 0%';
            $('#short_name').val(customer.short_name);
            $('#customer_name').val(customer.fullname);
            $('#contact_name').val(customer.contact_name);
            $('#credit_day').val(customer.credit_day);
            $('#payment_type').val(customer.payment_type);
            $('#vat_type').val(customer.vat_type);
            $('#vat_type_name').val(vatType);
        });

        $.get(urlGetProduct, function(data){
            let products = JSON.parse(data);
            for (let i = 0; i < products.length; i++) {
                products[i].selected = false;
            }

            app.products = products;
        });
    });   
    
    // เมื่อกดปุ่มลบในรายการ จะแจ้งข้อความ
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        let urlDelete = $(this).attr('href');
        Swal.fire({            
            text: "ยืนยันการลบข้อมูล?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'ลบข้อมูล!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = urlDelete;
            }
        });
    });
});
