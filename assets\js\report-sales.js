document.addEventListener('DOMContentLoaded', function() {
    // เมนูหลัก
    var mainMenuLinks = [
        document.getElementById('analysis-link'),
        document.getElementById('quotation-link'),
        document.getElementById('reservation-link'),
        document.getElementById('sales-link')
    ];
    // เมนูย่อย
    var analysisDetailLink = document.getElementById('analysis-detail-link');
    var quotationDetailLink = document.getElementById('quotation-detail-link');
    var quotationSummaryLink = document.getElementById('quotation-summary-link');
    var quotationDetailsLink = document.getElementById('quotation-details-link');
    var reservationDetailLink = document.getElementById('reservation-detail-link');
    var reservationSummaryLink = document.getElementById('reservation-summary-link');
    var reservationDetailsLink = document.getElementById('reservation-details-link');
    var salesDetailLink = document.getElementById('sales-detail-link');
    var salesSummaryLink = document.getElementById('sales-summary-link');
    var salesPopupLink = document.getElementById('sales-popup-link');

    // ซ่อนเมนูย่อยทั้งหมด
    function hideAllSubMenus() {
        [analysisDetailLink, quotationDetailLink, quotationSummaryLink, quotationDetailsLink,
         reservationDetailLink, reservationSummaryLink, reservationDetailsLink,
         salesDetailLink, salesSummaryLink, salesPopupLink].forEach(function(link) {
            if (link) link.style.display = 'none';
        });
    }
    // ลบ active จากเมนูหลักทั้งหมด
    function removeActiveFromMainMenu() {
        mainMenuLinks.forEach(function(link) {
            if (link) link.classList.remove('active');
        });
    }

    // --- เพิ่มเติม: ตั้งค่า default สำหรับหน้า report-analysis.php ---
    // ถ้าเมนูรายงานการวิเคราะห์ active (หน้า report-analysis.php)
    if (mainMenuLinks[0] && mainMenuLinks[0].classList.contains('active')) {
        hideAllSubMenus();
        if (analysisDetailLink) analysisDetailLink.style.display = 'block';
    }

    // แสดงเมนูย่อยตามเมนูหลักที่เลือก
    if (mainMenuLinks[0]) {
        mainMenuLinks[0].addEventListener('click', function(e) {
            e.preventDefault();
            hideAllSubMenus();
            removeActiveFromMainMenu();
            this.classList.add('active');
            if (analysisDetailLink) analysisDetailLink.style.display = 'block';
        });
    }
    if (mainMenuLinks[1]) {
        mainMenuLinks[1].addEventListener('click', function(e) {
            e.preventDefault();
            hideAllSubMenus();
            removeActiveFromMainMenu();
            this.classList.add('active');
            if (quotationDetailLink) quotationDetailLink.style.display = 'block';
            if (quotationSummaryLink) quotationSummaryLink.style.display = 'block';
            if (quotationDetailsLink) quotationDetailsLink.style.display = 'block';
        });
    }
    if (mainMenuLinks[2]) {
        mainMenuLinks[2].addEventListener('click', function(e) {
            e.preventDefault();
            hideAllSubMenus();
            removeActiveFromMainMenu();
            this.classList.add('active');
            if (reservationDetailLink) reservationDetailLink.style.display = 'block';
            if (reservationSummaryLink) reservationSummaryLink.style.display = 'block';
            if (reservationDetailsLink) reservationDetailsLink.style.display = 'block';
        });
    }
    if (mainMenuLinks[3]) {
        mainMenuLinks[3].addEventListener('click', function(e) {
            e.preventDefault();
            hideAllSubMenus();
            removeActiveFromMainMenu();
            this.classList.add('active');
            if (salesDetailLink) salesDetailLink.style.display = 'block';
            if (salesSummaryLink) salesSummaryLink.style.display = 'block';
            if (salesPopupLink) salesPopupLink.style.display = 'block';
        });
    }
});
