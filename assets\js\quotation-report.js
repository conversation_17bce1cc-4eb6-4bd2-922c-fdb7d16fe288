function openReportPopup() {
    var reportModal = new bootstrap.Modal(document.getElementById('reportModal'));
    
    // Initialize form when popup opens
    initializeForm();
    
    // Show modal
    reportModal.show();
    
    // Ensure Select2 is initialized after modal is fully shown
    $('#reportModal').on('shown.bs.modal', function (e) {
        // Small delay to ensure modal is fully rendered
        setTimeout(function() {
            console.log('Modal shown, initializing Select2...');
            initializeSelect2();
        }, 150);
    });
    
    // Clean up event listeners to prevent multiple bindings
    $('#reportModal').off('hidden.bs.modal.select2cleanup');
    $('#reportModal').on('hidden.bs.modal.select2cleanup', function (e) {
        // Clean up Select2 when modal is hidden
        if (typeof $ !== 'undefined' && $('#customerFilter').hasClass('select2-hidden-accessible')) {
            $('#customerFilter').select2('destroy');
        }
    });
}

function initializeForm() {
    // Set default date range for current month
    updateDateRange();
    
    // Initialize Select2 for customer dropdown
    initializeSelect2();
    
    // Set up event listeners for document filter checkbox
    const enableDocFilter = document.getElementById('enableDocFilter');
    const docRangeRow = document.getElementById('docRangeRow');
    
    enableDocFilter.addEventListener('change', function() {
        if (this.checked) {
            docRangeRow.style.display = 'block';
            docRangeRow.classList.add('show');
            docRangeRow.classList.remove('hide');
            
            // Smooth animation
            setTimeout(() => {
                docRangeRow.style.opacity = '1';
                docRangeRow.style.maxHeight = '200px';
            }, 10);
        } else {
            docRangeRow.classList.add('hide');
            docRangeRow.classList.remove('show');
            docRangeRow.style.opacity = '0';
            docRangeRow.style.maxHeight = '0';
            
            setTimeout(() => {
                docRangeRow.style.display = 'none';
                document.getElementById('docStart').value = '';
                document.getElementById('docEnd').value = '';
            }, 300);
        }
    });
    
    // Initialize tooltips
    initializeTooltips();
}

function initializeTooltips() {
    // Add tooltips to buttons and form elements
    const tooltips = {
        'reportDate': 'วันที่ที่จะแสดงในรายงาน',
        'reportUser': 'ชื่อผู้ออกรายงาน',
        'reportFormat': 'รูปแบบการแสดงผลรายงาน',
        'periodType': 'เลือกช่วงเวลาที่ต้องการ',
        'customerFilter': 'กรองรายงานตามลูกค้า',
        'statusFilter': 'กรองรายงานตามสถานะ'
    };
    
    Object.entries(tooltips).forEach(([id, title]) => {
        const element = document.getElementById(id);
        if (element) {
            element.setAttribute('title', title);
            element.setAttribute('data-bs-toggle', 'tooltip');
        }
    });
      // Initialize Bootstrap tooltips
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

function initializeSelect2() {
    // Wait for DOM and libraries to be ready
    if (typeof $ === 'undefined') {
        console.warn('jQuery not loaded, retrying in 100ms...');
        setTimeout(initializeSelect2, 100);
        return;
    }
    
    if (typeof $.fn.select2 === 'undefined') {
        console.warn('Select2 not loaded, retrying in 100ms...');
        setTimeout(initializeSelect2, 100);
        return;
    }

    try {
        // Check if element exists
        const customerFilter = $('#customerFilter');
        if (customerFilter.length === 0) {
            console.warn('Customer filter element not found');
            return;
        }

        // Destroy existing Select2 instance if exists
        if (customerFilter.hasClass('select2-hidden-accessible')) {
            customerFilter.select2('destroy');
        }

        // Initialize Select2 for customer dropdown with search capability
        customerFilter.select2({
            theme: 'bootstrap-5',
            placeholder: 'ค้นหาลูกค้า...',
            allowClear: true,
            width: '100%',
            dropdownParent: $('#reportModal'), // Important for modal
            language: {
                noResults: function() {
                    return "ไม่พบลูกค้าที่ค้นหา";
                },
                searching: function() {
                    return "กำลังค้นหา...";
                },
                inputTooShort: function() {
                    return "กรุณาพิมพ์อย่างน้อย 1 ตัวอักษร";
                },
                loadingMore: function() {
                    return "กำลังโหลดข้อมูลเพิ่มเติม...";
                }
            },
            matcher: function(params, data) {
                // If there are no search terms, return all data
                if ($.trim(params.term) === '') {
                    return data;
                }

                // Skip if there is no 'text' property
                if (typeof data.text === 'undefined') {
                    return null;
                }

                // Create search term for Thai text search
                var searchTerm = params.term.toLowerCase();
                var customerText = data.text.toLowerCase();

                // Search in customer name and short name
                if (customerText.indexOf(searchTerm) > -1) {
                    return data;
                }

                // Return null if the term should not be displayed
                return null;
            }
        });

        console.log('Select2 initialized successfully for customer filter');
        
    } catch (error) {
        console.error('Error initializing Select2:', error);
        // Fallback: keep as regular select
    }
}

function validateForm() {
    const dateStart = document.getElementById('dateStart').value;
    const dateEnd = document.getElementById('dateEnd').value;
    const enableDocFilter = document.getElementById('enableDocFilter').checked;
    const docStart = document.getElementById('docStart').value;
    const docEnd = document.getElementById('docEnd').value;
    
    // Validate date range
    if (!dateStart || !dateEnd) {
        alert('กรุณาระบุช่วงวันที่');
        return false;
    }
    
    if (new Date(dateStart) > new Date(dateEnd)) {
        alert('วันที่เริ่มต้นต้องน้อยกว่าหรือเท่ากับวันที่สิ้นสุด');
        return false;
    }
    
    // Validate document range if enabled
    if (enableDocFilter) {
        if (!docStart.trim() || !docEnd.trim()) {
            alert('กรุณาระบุช่วงเลขที่เอกสารให้ครบถ้วน');
            return false;
        }
    }
    
    return true;
}

function getFormData() {
    // Get customer filter value (handle both regular select and Select2)
    let customerFilterValue = '';
    if (typeof $ !== 'undefined' && $('#customerFilter').hasClass('select2-hidden-accessible')) {
        customerFilterValue = $('#customerFilter').val() || '';
    } else {
        customerFilterValue = document.getElementById('customerFilter').value;
    }
    
    return {
        reportDate: document.getElementById('reportDate').value,
        reportUser: document.getElementById('reportUser').value,
        reportFormat: document.getElementById('reportFormat').value,
        dateStart: document.getElementById('dateStart').value,
        dateEnd: document.getElementById('dateEnd').value,
        docStart: document.getElementById('enableDocFilter').checked ? document.getElementById('docStart').value : '',
        docEnd: document.getElementById('enableDocFilter').checked ? document.getElementById('docEnd').value : '',
        customerFilter: customerFilterValue,
        statusFilter: document.getElementById('statusFilter').value,
        showProductDetails: document.getElementById('showProductDetails').checked,
        showCustomerDetails: document.getElementById('showCustomerDetails').checked,
        showTotalSummary: document.getElementById('showTotalSummary').checked,
        groupByCustomer: document.getElementById('groupByCustomer').checked
    };
}

function printReport(type) {
    if (!validateForm()) {
        return;
    }
    
    const formData = getFormData();
    
    // Build query string
    const params = new URLSearchParams({
        type: type,
        start: formData.dateStart,
        end: formData.dateEnd,
        format: formData.reportFormat,
        customer_id: formData.customerFilter,
        status: formData.statusFilter,
        show_products: formData.showProductDetails ? '1' : '0',
        show_customers: formData.showCustomerDetails ? '1' : '0',
        show_summary: formData.showTotalSummary ? '1' : '0',
        group_by_customer: formData.groupByCustomer ? '1' : '0'
    });
    
    // Add document range if specified
    if (formData.docStart) {
        params.append('doc_start', formData.docStart);
    }
    if (formData.docEnd) {
        params.append('doc_end', formData.docEnd);
    }
    
    // Show loading indicator
    showLoading(true);
      // Handle different report types
    const url = `report-generate.php?${params.toString()}`;
    
    switch(type) {
        case 'print':
        case 'text':
        case 'pdf':
            window.open(url, '_blank');
            break;
        case 'excel':
            // For Excel, use specialized Excel export
            const excelUrl = `report-excel.php?${params.toString()}`;
            downloadFile(excelUrl);
            break;
        case 'edit':
            window.open(`report-edit.php?${params.toString()}`, '_blank');
            break;
    }
    
    // Hide loading indicator after a short delay
    setTimeout(() => showLoading(false), 1000);
}

function previewReport() {
    if (!validateForm()) {
        return;
    }
    
    const formData = getFormData();
    const params = new URLSearchParams({
        type: 'preview',
        start: formData.dateStart,
        end: formData.dateEnd,
        format: formData.reportFormat,
        customer_id: formData.customerFilter,
        status: formData.statusFilter,
        show_products: formData.showProductDetails ? '1' : '0',
        show_customers: formData.showCustomerDetails ? '1' : '0',
        show_summary: formData.showTotalSummary ? '1' : '0',
        group_by_customer: formData.groupByCustomer ? '1' : '0'
    });
    
    if (formData.docStart) params.append('doc_start', formData.docStart);
    if (formData.docEnd) params.append('doc_end', formData.docEnd);
    
    window.open(`report-generate.php?${params.toString()}`, '_blank', 'width=1200,height=800,scrollbars=yes');
}

function downloadFile(url) {
    const link = document.createElement('a');
    link.href = url;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function showLoading(show) {
    // You can implement a loading indicator here
    const buttons = document.querySelectorAll('.modal-footer button');
    buttons.forEach(button => {
        button.disabled = show;
    });
}

function setupPrinter() {
    // Open printer setup dialog
    if (window.print) {
        window.print();
    } else {
        alert('การตั้งค่าเครื่องพิมพ์ไม่รองรับในเบราว์เซอร์นี้');
    }
}

function updateDateRange() {
    const periodType = document.getElementById('periodType').value;
    const today = new Date();
    let startDate = new Date();
    let endDate = new Date();
    
    switch(periodType) {
        case 'today':
            startDate = endDate = new Date(today);
            break;
        case 'yesterday':
            startDate = endDate = new Date(today.setDate(today.getDate() - 1));
            break;
        case 'this_week':
            const dayOfWeek = today.getDay();
            startDate = new Date(today.setDate(today.getDate() - dayOfWeek));
            endDate = new Date(today.setDate(today.getDate() + 6));
            break;
        case 'last_week':
            const lastWeekStart = new Date(today.setDate(today.getDate() - today.getDay() - 7));
            startDate = lastWeekStart;
            endDate = new Date(lastWeekStart.getTime() + 6 * 24 * 60 * 60 * 1000);
            break;
        case 'month':
            startDate = new Date(today.getFullYear(), today.getMonth(), 1);
            endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            break;
        case 'last_month':
            startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            endDate = new Date(today.getFullYear(), today.getMonth(), 0);
            break;
        case 'quarter':
            const quarter = Math.floor(today.getMonth() / 3);
            startDate = new Date(today.getFullYear(), quarter * 3, 1);
            endDate = new Date(today.getFullYear(), (quarter + 1) * 3, 0);
            break;
        case 'last_quarter':
            const lastQuarter = Math.floor(today.getMonth() / 3) - 1;
            if (lastQuarter < 0) {
                startDate = new Date(today.getFullYear() - 1, 9, 1);
                endDate = new Date(today.getFullYear() - 1, 11, 31);
            } else {
                startDate = new Date(today.getFullYear(), lastQuarter * 3, 1);
                endDate = new Date(today.getFullYear(), (lastQuarter + 1) * 3, 0);
            }
            break;
        case 'year':
            startDate = new Date(today.getFullYear(), 0, 1);
            endDate = new Date(today.getFullYear(), 11, 31);
            break;
        case 'last_year':
            startDate = new Date(today.getFullYear() - 1, 0, 1);
            endDate = new Date(today.getFullYear() - 1, 11, 31);
            break;
        case 'custom':
            // Don't change dates for custom selection
            return;
    }
    
    document.getElementById('dateStart').value = startDate.toISOString().split('T')[0];
    document.getElementById('dateEnd').value = endDate.toISOString().split('T')[0];
}

// Initialize event listeners when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Update date range when period type changes
    const periodTypeElement = document.getElementById('periodType');
    if (periodTypeElement) {
        periodTypeElement.addEventListener('change', function() {
            updateDateRange();
        });
    }
    
    // Initialize date validation
    const dateStartElement = document.getElementById('dateStart');
    const dateEndElement = document.getElementById('dateEnd');
    
    if (dateStartElement && dateEndElement) {
        dateStartElement.addEventListener('change', function() {
            if (this.value > dateEndElement.value) {
                dateEndElement.value = this.value;
            }
        });
        
        dateEndElement.addEventListener('change', function() {
            if (this.value < dateStartElement.value) {
                dateStartElement.value = this.value;
            }
        });
    }
    
    // Format display enhancement
    const reportFormatElement = document.getElementById('reportFormat');
    if (reportFormatElement) {
        reportFormatElement.addEventListener('change', function() {
            const format = this.value;
            const groupByCustomer = document.getElementById('groupByCustomer');
            
            // Auto-adjust options based on format
            if (format === 'customer') {
                groupByCustomer.checked = true;
                groupByCustomer.disabled = true;
            } else {
                groupByCustomer.disabled = false;
            }
        });
    }
});