const { createApp, ref, onMounted } = Vue
const app = createApp({
    setup() {
        const products = ref([]);
        const subTotal = ref(0);
        const discount = ref(0);
        const vat = ref(0);
        const afterDiscount = ref(0);
        const grandTotal = ref(0);
        const reservationItems = ref([]);
        const quotationId = ref(0);

        // เลือกสินค้าจาก modal
        const onSelectProduct = () => {
            for (let i = 0; i < products.value.length; i++) {
                if (products.value[i].selected) {
                    let qu = {
                        id: 0,
                        product_id: products.value[i].id,
                        product_code: products.value[i].product_code,
                        product_name: products.value[i].product_name,
                        quantity: products.value[i].quantity || 1, // Use quantity from quotation_detail if available
                        unit_name: products.value[i].unit_name,
                        price: products.value[i].price,
                        discount: 0,
                        total: products.value[i].price * (products.value[i].quantity || 1), // Calculate total based on quantity
                        pdf: products.value[i].profile_image ? $base_url + '/upload_image/product/' + products.value[i].profile_image : null,
                    };
                    reservationItems.value.push(qu);
                }
            }

            $('#productModal').modal('hide');
            products.value.forEach(product => {
                product.selected = false;
            });

        };


        // ลบรายการ ค้าในใบเสนอราคา
        const removeQuotationItem = (index) => {
            Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
            if (result.isConfirmed) {
                reservationItems.value.splice(index, 1);
                Swal.fire(
                'Deleted!',
                'Your item has been deleted.',
                'success'
                )
            }
            })
        }

        // คำนวณราคารวม item.total
        const calculateTotal = (item) => {
            let total = item.price * item.quantity;
            if (item.discount > 0) {
                total -= item.discount;
            }
            return total;
        }

        // คำนวณราคารวมทั้งหมด
        const calculateSubTotal = () => {
            let total = 0;
            for (let i = 0; i < reservationItems.value.length; i++) {
                total += calculateTotal(reservationItems.value[i]);
            }
            subTotal.value = total;
            return subTotal.value;
        }

        // คำนวณยอดรวมทั้งหมด
        const calculateGrandTotal = () => {
            const discount = parseFloat($('#discount').val()) || 0;
            const vatType = parseInt($('#vat_type').val());
            const subTotal = calculateSubTotal();
            afterDiscount.value = (subTotal - discount).toFixed(2);

            if (vatType === 1) {
                vat.value = (afterDiscount.value * 0.07).toFixed(2);
            }

            grandTotal.value = (parseFloat(afterDiscount.value) + parseFloat(vat.value)).toFixed(2);

            return grandTotal.value;
        }

        const fetchProductsByCustomer = (customer_id) => {
            const quotation_id = $('#quotation_id').val();
            const urlGetProduct = $base_url + '/api/product.php?customer_id=' + customer_id + '&quotation_id=' + quotation_id;
            $.get(urlGetProduct, function(data) {
                try {
                    let productsData = JSON.parse(data);
                    for (let i = 0; i < productsData.length; i++) {
                        productsData[i].selected = false;
                        // ใช้ quantity จาก quotation ถ้ามี
                        productsData[i].quantity = productsData[i].quotation_quantity || 1;
                    }
                    products.value = productsData;
                } catch (e) {
                    console.error("Error parsing JSON response:", e);
                    console.log("Raw response:", data);
                    // Show error to user
                    Swal.fire({
                        title: 'Error',
                        text: 'Failed to load product data. Please try again or contact support.',
                        icon: 'error'
                    });
                }
            }).fail(function(jqXHR, textStatus, errorThrown) {
                console.error("AJAX request failed:", textStatus, errorThrown);
                Swal.fire({
                    title: 'Error',
                    text: 'Failed to connect to the server. Please check your connection and try again.',
                    icon: 'error'
                });
            });
        };

        // Fetch reservation details
        const fetchReservationDetails = () => {
            const reservationId = new URLSearchParams(window.location.search).get('id');
            if (!reservationId) return;

            fetch(`${$base_url}/api/reservation-detail.php?reservation_id=${reservationId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    reservationItems.value = data.map(item => ({
                        ...item,
                        pdf: item.profile_image ? `${$base_url}/upload_image/product/${item.profile_image}` : null
                    }));
                })
                .catch(error => {
                    console.error('Error fetching reservation details:', error);
                    Swal.fire({
                        title: 'Error',
                        text: 'Failed to load reservation details. Please try again or contact support.',
                        icon: 'error'
                    });
                });
        };

        // Remove an item from the reservation
        const removeReservationItem = (index) => {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    reservationItems.value.splice(index, 1);
                    Swal.fire('Deleted!', 'Your item has been deleted.', 'success');
                }
            });
        };

        return {
            subTotal,
            vat,
            afterDiscount,
            discount,
            grandTotal,
            reservationItems,
            products,
            calculateGrandTotal,
            onSelectProduct,
            removeQuotationItem,
            calculateTotal,
            calculateSubTotal,
            fetchProductsByCustomer,
            fetchReservationDetails,
            removeReservationItem
        }
    },
    mounted() {
        this.fetchReservationDetails();
        // Only fetch products if a customer is selected
        const customerId = $('#customer_id').val();
        if (customerId) {
            this.fetchProductsByCustomer(customerId);
        }
    }
}).mount('#appvue');

//## jquery section ##
$(function(){
    $('#customer_id').on('change', function(){
        var customer_id = $(this).val();
        var urlGetCustomer = $base_url + '/api/customer.php?id=' + customer_id;
        var urlQuotation = $base_url + '/api/quotations.php?customer_id=' + customer_id;

        let customer;
        $.get(urlGetCustomer).then((data) => {
            try {
                customer = JSON.parse(data);
                const vatType = customer.vat_type == 1 ? 'Vat 7%' : 'Vat 0%';
                $('#short_name').val(customer.short_name);
                $('#customer_name').val(customer.fullname);
                $('#contact_name').val(customer.contact_name);
                $('#credit_day').val(customer.credit_day);
                $('#payment_type').val(customer.payment_type);
                $('#vat_type').val(customer.vat_type);
                $('#vat_type_name').val(vatType);

                // Fetch quotations after customer data is loaded
                return $.get(urlQuotation);
            } catch (e) {
                console.error("Error parsing customer JSON:", e);
                console.log("Raw response:", data);
                Swal.fire({
                    title: 'Error',
                    text: 'Failed to load customer data. Please try again or contact support.',
                    icon: 'error'
                });
                throw e; // Rethrow to stop the promise chain
            }
        }).then((data) => {
            try {
                let quotations = JSON.parse(data);
                $('#quotation_id').empty();
                $('#quotation_id').append('<option value="">-- เลือกใบเสนอราคา --</option>');
                for (let i = 0; i < quotations.length; i++) {
                    $('#quotation_id').append('<option value="' + quotations[i].id + '">' + quotations[i].document_no + ' - ' + customer.short_name + '</option>');
                }
            } catch (e) {
                console.error("Error parsing quotations JSON:", e);
                console.log("Raw response:", data);
                Swal.fire({
                    title: 'Error',
                    text: 'Failed to load quotation data. Please try again or contact support.',
                    icon: 'error'
                });
            }
        })
        .catch((error) => {
            console.error('Error fetching data:', error);
            Swal.fire({
                title: 'Error',
                text: 'Failed to connect to the server. Please check your connection and try again.',
                icon: 'error'
            });
        });
    });

    $('#quotation_id').on('change', function(){
        var quotation_id = $(this).val();
        var urlGetProduct = $base_url + '/api/product.php?quotation_id=' + quotation_id;

        if (quotation_id > 0) {
            $.get(urlGetProduct, function(data){
                try {
                    let products = JSON.parse(data);
                    for (let i = 0; i < products.length; i++) {
                        products[i].selected = false;
                    }

                    app.products = products;
                } catch (e) {
                    console.error("Error parsing JSON response:", e);
                    console.log("Raw response:", data);
                    // Show error to user
                    Swal.fire({
                        title: 'Error',
                        text: 'Failed to load product data. Please try again or contact support.',
                        icon: 'error'
                    });
                }
            }).fail(function(_, textStatus, errorThrown) {
                console.error("AJAX request failed:", textStatus, errorThrown);
                Swal.fire({
                    title: 'Error',
                    text: 'Failed to connect to the server. Please check your connection and try again.',
                    icon: 'error'
                });
            });
        }
    });

    // เมื่อกดปุ่มลบในรายการ จะแจ้งข้อความ
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        let urlDelete = $(this).attr('href');
        Swal.fire({
            text: "การลบข้อมูล?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'ลบข้อมูล!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = urlDelete;
            }
        });
    });
});
