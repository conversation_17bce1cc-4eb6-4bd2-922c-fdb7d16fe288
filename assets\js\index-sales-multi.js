// Sales Charts Manager - Multiple Charts
class SalesChartsManager {
    constructor() {
        this.apiUrl = 'api/index-sales-garf.php';
        this.charts = {};
        this.data = null;
    }

    async fetchData() {
        try {
            const response = await fetch(this.apiUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            if (!data.success) {
                throw new Error(data.error || 'API returned error');
            }
            this.data = data;
            return data;
        } catch (error) {
            console.error('Error fetching data:', error);
            throw error;
        }
    }

    formatCurrency(amount) {
        return '฿' + new Intl.NumberFormat('th-TH', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }

    formatNumber(number) {
        return new Intl.NumberFormat('th-TH').format(number);
    }    async initializeAllCharts() {
        try {
            await this.fetchData();
            
            this.createMonthlySalesChart();
            this.createReservationsChart();
            this.createCombinedChart();
            this.createComparisonChart();
            this.createPieChart();
            await this.createDonutChart(); // Make sure this is awaited since it's now async
            this.updateSummaryStats();
            
        } catch (error) {
            console.error('Error initializing charts:', error);
            this.showError('ไม่สามารถโหลดข้อมูลกราฟได้ กรุณาลองใหม่อีกครั้ง');
        }
    }

    createMonthlySalesChart() {
        const canvas = document.getElementById('monthlySalesChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const labels = this.data.monthly_data.map(item => item.month);
        const salesData = this.data.monthly_data.map(item => item.total_sales);

        this.charts.monthlySales = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: `ยอดขาย ปี ${this.data.current_year}`,
                    data: salesData,
                    backgroundColor: 'rgba(60,141,188,0.3)',
                    borderColor: 'rgba(60,141,188,0.8)',
                    pointRadius: 4,
                    pointColor: '#3b8bba',
                    pointStrokeColor: 'rgba(60,141,188,1)',
                    pointHighlightFill: '#fff',
                    pointHighlightStroke: 'rgba(60,141,188,1)',
                    fill: true
                }]
            },
            options: {
                maintainAspectRatio: false,
                responsive: true,
                plugins: {
                    legend: {
                        display: true
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                return `${context.dataset.label}: ${this.formatCurrency(context.parsed.y)}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: (value) => this.formatCurrency(value)
                        }
                    }
                }
            }
        });
    }

    createReservationsChart() {
        const canvas = document.getElementById('reservationsChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const labels = this.data.monthly_data.map(item => item.month);
        const reservationsData = this.data.monthly_data.map(item => item.total_reservations);

        this.charts.reservations = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: `รับจองสินค้า ปี ${this.data.current_year}`,
                    data: reservationsData,
                    backgroundColor: 'rgba(23, 162, 184, 0.3)',
                    borderColor: 'rgba(23, 162, 184, 1)',
                    pointRadius: 4,
                    fill: false
                }]
            },
            options: {
                maintainAspectRatio: false,
                responsive: true,
                plugins: {
                    legend: {
                        display: true
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                return `${context.dataset.label}: ${this.formatCurrency(context.parsed.y)}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: (value) => this.formatCurrency(value)
                        }
                    }
                }
            }
        });
    }

    createCombinedChart() {
        const canvas = document.getElementById('combinedChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const labels = this.data.monthly_data.map(item => item.month);
        const salesData = this.data.monthly_data.map(item => item.total_sales);
        const reservationsData = this.data.monthly_data.map(item => item.total_reservations);

        this.charts.combined = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: `ยอดขาย ปี ${this.data.current_year}`,
                        data: salesData,
                        backgroundColor: 'rgba(40, 167, 69, 0.8)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 1
                    },
                    {
                        label: `รับจองสินค้า ปี ${this.data.current_year}`,
                        data: reservationsData,
                        backgroundColor: 'rgba(255, 193, 7, 0.8)',
                        borderColor: 'rgba(255, 193, 7, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                maintainAspectRatio: false,
                responsive: true,
                plugins: {
                    legend: {
                        display: true
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                return `${context.dataset.label}: ${this.formatCurrency(context.parsed.y)}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: (value) => this.formatCurrency(value)
                        }
                    }
                }
            }
        });
    }

    createComparisonChart() {
        const canvas = document.getElementById('comparisonChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const labels = this.data.monthly_data.map(item => item.month);
        const currentYearData = this.data.monthly_data.map(item => item.total_sales);
        const previousYearData = this.data.previous_year_data;

        this.charts.comparison = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: `ปี ${this.data.current_year}`,
                        data: currentYearData,
                        backgroundColor: 'rgba(108, 117, 125, 0.8)',
                        borderColor: 'rgba(108, 117, 125, 1)',
                        borderWidth: 1
                    },
                    {
                        label: `ปี ${this.data.previous_year}`,
                        data: previousYearData,
                        backgroundColor: 'rgba(201, 203, 207, 0.8)',
                        borderColor: 'rgba(201, 203, 207, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                maintainAspectRatio: false,
                responsive: true,
                plugins: {
                    legend: {
                        display: true
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                return `${context.dataset.label}: ${this.formatCurrency(context.parsed.y)}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        stacked: false
                    },
                    y: {
                        beginAtZero: true,
                        stacked: false,
                        ticks: {
                            callback: (value) => this.formatCurrency(value)
                        }
                    }
                }
            }
        });
    }

    createPieChart() {
        const canvas = document.getElementById('pieChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        // Calculate totals for pie chart
        const totalSales = this.data.monthly_data.reduce((sum, month) => sum + month.total_sales, 0);
        const totalReservations = this.data.monthly_data.reduce((sum, month) => sum + month.total_reservations, 0);
        const totalOrders = this.data.monthly_data.reduce((sum, month) => sum + month.total_orders, 0);

        this.charts.pie = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['ยอดขาย', 'รับจองสินค้า', 'คำสั่งซื้อ'],
                datasets: [{
                    data: [totalSales, totalReservations, totalOrders * 1000], // Scale orders for visibility
                    backgroundColor: [
                        '#dc3545',
                        '#ffc107',
                        '#007bff'
                    ]
                }]
            },
            options: {
                maintainAspectRatio: false,
                responsive: true,
                plugins: {
                    legend: {
                        display: true
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const label = context.label;
                                const value = context.parsed;
                                if (label === 'คำสั่งซื้อ') {
                                    return `${label}: ${this.formatNumber(value / 1000)} ใบ`;
                                }
                                return `${label}: ${this.formatCurrency(value)}`;
                            }
                        }
                    }
                }
            }
        });
    }    async createDonutChart() {
        const canvas = document.getElementById('donutChart');
        if (!canvas) return;

        try {
            // Fetch customer invoice data
            const response = await fetch('api/customer-invoices-chart.php');
            const customerData = await response.json();
            
            if (!customerData.success) {
                throw new Error(customerData.error || 'Failed to fetch customer data');
            }

            const ctx = canvas.getContext('2d');
            
            // Prepare data for donut chart
            const labels = customerData.data.map(item => item.short_name || item.customer_name);
            const percentages = customerData.data.map(item => item.percentage);
            
            // Generate colors for each customer
        const colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#4ECDC4', '#C9CBCF', '#45B7D1', '#96CEB4',
            '#FF9F40', '#4ECDC4', '#C9CBCF', '#45B7D1', '#96CEB4',
            '#D4A5A5', '#9B59B6', '#3498DB', '#E74C3C', '#2ECC71',
            '#F1C40F', '#1ABC9C', '#34495E', '#7F8C8D', '#E67E22'
        ];

            this.charts.donut = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: percentages,
                        backgroundColor: colors,
                        borderColor: colors.map(color => color),
                        borderWidth: 2,
                        hoverOffset: 4
                    }]
                },
                options: {
                    maintainAspectRatio: false,
                    responsive: true,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                usePointStyle: true,
                                generateLabels: function(chart) {
                                    const data = chart.data;
                                    if (data.labels.length && data.datasets.length) {
                                        return data.labels.map(function(label, i) {
                                            const meta = chart.getDatasetMeta(0);
                                            const style = meta.controller.getStyle(i);
                                            return {
                                                text: `${label} (${data.datasets[0].data[i]}%)`,
                                                fillStyle: style.backgroundColor,
                                                strokeStyle: style.borderColor,
                                                lineWidth: style.borderWidth,
                                                pointStyle: 'circle',
                                                hidden: isNaN(data.datasets[0].data[i]) || meta.data[i].hidden
                                            };
                                        });
                                    }
                                    return [];
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: (context) => {
                                    const customerInfo = customerData.data[context.dataIndex];
                                    return [
                                        `${context.label}`,
                                        `จำนวนใบแจ้งหนี้: ${this.formatNumber(customerInfo.invoice_count)} ใบ`,
                                        `ยอดเงิน: ${this.formatCurrency(customerInfo.total_amount)}`,
                                        `สัดส่วน: ${customerInfo.percentage}%`
                                    ];
                                }
                            }
                        }
                    },
                    elements: {
                        arc: {
                            borderWidth: 2
                        }
                    }
                }
            });
        } catch (error) {
            console.error('Error creating donut chart:', error);
            this.showError('ไม่สามารถโหลดข้อมูลกราฟลูกค้าได้');
        }
    }

    updateSummaryStats() {
        const currentMonth = new Date().getMonth();
        const currentMonthData = this.data.monthly_data[currentMonth];
        const totalSalesThisYear = this.data.monthly_data.reduce((sum, month) => sum + month.total_sales, 0);
        const totalOrdersThisYear = this.data.monthly_data.reduce((sum, month) => sum + month.total_orders, 0);
        const totalReservationsThisYear = this.data.monthly_data.reduce((sum, month) => sum + month.total_reservations, 0);

        // Update summary elements if they exist
        const summaryElements = {
            'current-month-sales': this.formatCurrency(currentMonthData.total_sales),
            'current-month-orders': this.formatNumber(currentMonthData.total_orders),
            'total-year-sales': this.formatCurrency(totalSalesThisYear),
            'total-year-orders': this.formatNumber(totalOrdersThisYear),
            'current-month-reservations': this.formatCurrency(currentMonthData.total_reservations),
            'total-year-reservations': this.formatCurrency(totalReservationsThisYear)
        };

        Object.entries(summaryElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    showError(message) {
        console.error(message);
        // You can add UI error display here if needed
    }    async refresh() {
        // Destroy all existing charts
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.destroy();
            }
        });
        this.charts = {};
        
        // Reinitialize all charts
        await this.initializeAllCharts();
    }
}

// Initialize charts when DOM is loaded
document.addEventListener('DOMContentLoaded', async function() {
    const chartsManager = new SalesChartsManager();
    await chartsManager.initializeAllCharts();
    
    // Refresh charts every 5 minutes
    setInterval(async () => {
        await chartsManager.refresh();
    }, 300000);
    
    // Make it globally accessible
    window.chartsManager = chartsManager;
});

// Export for global access
window.SalesChartsManager = SalesChartsManager;
