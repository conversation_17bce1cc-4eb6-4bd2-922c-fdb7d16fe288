document.addEventListener('DOMContentLoaded', function() {
    // เมนูหลัก
    var mainMenuLinks = [
        document.getElementById('analysis-link'),
        document.getElementById('requisition-link'),
        document.getElementById('order-link'),
        document.getElementById('order-reservation-link')
    ];
    // เมนูย่อย
    var analysisDetailLink = document.getElementById('analysis-detail-link');
    var requisitionDetailLink = document.getElementById('requisition-detail-link');
    var requisitionSummaryLink = document.getElementById('requisition-summary-link');
    var requisitionDetailsLink = document.getElementById('requisition-details-link');
    var orderDetailLink = document.getElementById('order-detail-link');
    var orderSummaryLink = document.getElementById('order-summary-link');
    var orderDetailsLink = document.getElementById('order-details-link');
    var orderReservationDetailLink = document.getElementById('order-reservation-detail-link');
    var orderReservationSummaryLink = document.getElementById('order-reservation-summary-link');
    var orderReservationPopupLink = document.getElementById('order-reservation-popup-link');

    // ซ่อนเมนูย่อยทั้งหมด
    function hideAllSubMenus() {
        [analysisDetailLink, requisitionDetailLink, requisitionSummaryLink, requisitionDetailsLink,
         orderDetailLink, orderSummaryLink, orderDetailsLink,
         orderReservationDetailLink, orderReservationSummaryLink, orderReservationPopupLink].forEach(function(link) {
            if (link) link.style.display = 'none';
        });
    }
    // ลบ active จากเมนูหลักทั้งหมด
    function removeActiveFromMainMenu() {
        mainMenuLinks.forEach(function(link) {
            if (link) link.classList.remove('active');
        });
    }

    // --- เพิ่มเติม: ตั้งค่า default สำหรับหน้า report-analysis.php ---
    // ถ้าเมนูรายงานการวิเคราะห์ active (หน้า report-analysis.php)
    if (mainMenuLinks[0] && mainMenuLinks[0].classList.contains('active')) {
        hideAllSubMenus();
        if (analysisDetailLink) analysisDetailLink.style.display = 'block';
    }

    // แสดงเมนูย่อยตามเมนูหลักที่เลือก
    if (mainMenuLinks[0]) {
        mainMenuLinks[0].addEventListener('click', function(e) {
            e.preventDefault();
            hideAllSubMenus();
            removeActiveFromMainMenu();
            this.classList.add('active');
            if (analysisDetailLink) analysisDetailLink.style.display = 'block';
        });
    }
    if (mainMenuLinks[1]) {
        mainMenuLinks[1].addEventListener('click', function(e) {
            e.preventDefault();
            hideAllSubMenus();
            removeActiveFromMainMenu();
            this.classList.add('active');
            if (requisitionDetailLink) requisitionDetailLink.style.display = 'block';
            if (requisitionSummaryLink) requisitionSummaryLink.style.display = 'block';
            if (requisitionDetailsLink) requisitionDetailsLink.style.display = 'block';
        });
    }
    if (mainMenuLinks[2]) {
        mainMenuLinks[2].addEventListener('click', function(e) {
            e.preventDefault();
            hideAllSubMenus();
            removeActiveFromMainMenu();
            this.classList.add('active');
            if (orderDetailLink) orderDetailLink.style.display = 'block';
            if (orderSummaryLink) orderSummaryLink.style.display = 'block';
            if (orderDetailsLink) orderDetailsLink.style.display = 'block';
        });
    }
    if (mainMenuLinks[3]) {
        mainMenuLinks[3].addEventListener('click', function(e) {
            e.preventDefault();
            hideAllSubMenus();
            removeActiveFromMainMenu();
            this.classList.add('active');
            if (orderReservationDetailLink) orderReservationDetailLink.style.display = 'block';
            if (orderReservationSummaryLink) orderReservationSummaryLink.style.display = 'block';
            if (orderReservationPopupLink) orderReservationPopupLink.style.display = 'block';
        });
    }
});
