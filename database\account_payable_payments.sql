-- สคีมาสำหรับระบบบัญชีเจ้าหนี้ (Account Payable System)


-- ตารางสำหรับการชำระหนี้
CREATE TABLE `account_payable_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `payment_number` varchar(50) NOT NULL COMMENT 'เลขที่การชำระเงิน',
  `account_payable_id` int(11) NOT NULL COMMENT 'รหัสบัญชีเจ้าหนี้',
  `payment_date` date NOT NULL COMMENT 'วันที่จ่ายเงิน',
  `payment_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินที่จ่าย',
  `payment_method` enum('cash','bank_transfer','cheque','credit_card','other') NOT NULL DEFAULT 'bank_transfer' COMMENT 'วิธีการจ่ายเงิน',
  `bank_account` varchar(100) DEFAULT NULL COMMENT 'บัญชีธนาคาร',
  `cheque_number` varchar(50) DEFAULT NULL COMMENT 'เลขที่เช็ค',
  `transaction_ref` varchar(100) DEFAULT NULL COMMENT 'เลขที่อ้างอิงธุรกรรม',
  `description` text DEFAULT NULL COMMENT 'หมายเหตุ',
  `created_by` int(11) NOT NULL COMMENT 'ผู้สร้าง',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_account_payable_id` (`account_payable_id`),
  KEY `idx_payment_date` (`payment_date`),
  UNIQUE KEY `unique_payment_number` (`payment_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


