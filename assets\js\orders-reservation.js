const { createApp, ref } = Vue;
const app = createApp({    setup() {
        const productsorders = ref([]);
        const subTotal = ref(0);
        const discount = ref(0);
        const vat = ref(0);
        const withholding = ref(0);
        const afterDiscount = ref(0);
        const grandTotal = ref(0);
        const orderreservationItems = ref([]);
        const documentDueDate = ref('');

        // Pagination variables
        const currentPage = ref(1);
        const itemsPerPage = ref(10);
        const totalProducts = ref(0);
        const totalPages = ref(0);
        const selectAll = ref(false);
        const searchQuery = ref('');

       // เลือกสินค้าจาก modal
        const onSelectProductorder = () => {
            for(let i = 0; i < productsorders.value.length; i++) {
                if (productsorders.value[i].selected) {
                    let qu = {
                        id: productsorders.value[i].id || productsorders.value[i].product_id,
                        product_code: productsorders.value[i].product_code,
                        product_name: productsorders.value[i].product_name,
                        quantity: productsorders.value[i].quantity || 1,
                        unit_name: productsorders.value[i].unit_name,
                        store_id: productsorders.value[i].store_id || '',
                        store_name: productsorders.value[i].store_name || '',
                        price: productsorders.value[i].price,
                        discount: 0,
                        total: productsorders.value[i].price * (productsorders.value[i].quantity || 1),
                        pdf: productsorders.value[i].profile_image ? `${$base_url}/upload_image/product/${productsorders.value[i].profile_image}` : null,
                    };
                    orderreservationItems.value.push(qu);
                }
            }
            $('#productModal').modal('hide');
            productsorders.value.forEach(productorder => {
                productorder.selected = false;
            });
        };

        // คำนวณราคารวม item.total
        const calculateTotal = (item) => {
            let total = item.price * item.quantity;
            if (item.discount > 0) {
                total -= item.discount;
            }
            return total;
        }

        // คำนวณราคารวมหมด
        const calculateSubTotal = () => {
            let total = 0;
            for (let i = 0; i < orderreservationItems.value.length; i++) {
                total += calculateTotal(orderreservationItems.value[i]);
            }
            subTotal.value = total;
            return subTotal.value;
        }        // คำนวณยอดรวมสุทธิ
        const calculateGrandTotal = () => {
            const discount = parseFloat($('#discount').val()) || 0;
            const vatType = parseInt($('#vat_type').val());
            const withholding_type = parseFloat($('#withholding_type').val()) || 0;
            const subTotal = calculateSubTotal();
            
            afterDiscount.value = (subTotal - discount).toFixed(2);            
            // คำนวณ VAT ตาม Type
            if (vatType === 1) {
                // Type 1: ราคาสินค้ารวมภาษี (VAT 7% included)
                vat.value = (afterDiscount.value * 0.07 / 1.07).toFixed(2);
                grandTotal.value = parseFloat(afterDiscount.value).toFixed(2);
                withholding.value = 0; // ไม่หัก ณ จ่าย สำหรับ Type 1
            } else if (vatType === 2) {
                // Type 2: ราคาสินค้าแยกภาษี (VAT 7% separate)
                vat.value = (afterDiscount.value * 0.07).toFixed(2);
                // คำนวณยอดหัก ณ จ่าย (หักจากยอดหลังหักส่วนลด + withholding_type)
                const totalWithVat = parseFloat(afterDiscount.value) + parseFloat(vat.value);
                withholding.value = (afterDiscount.value * withholding_type).toFixed(2);
                grandTotal.value = (subTotal + parseFloat(vat.value)).toFixed(2);
            } else if (vatType === 3) {
                // Type 3: อัตราภาษี 0% (VAT 0%)
                vat.value = 0;
                withholding.value = 0; // ไม่หัก ณ จ่าย สำหรับ Type 3
                grandTotal.value = parseFloat(afterDiscount.value).toFixed(2);
            }

            return grandTotal.value;
        };        // คำนวณยอดหัก ณ จ่าย
        const calculateWithholding = () => {
            const withholding_type = parseFloat($('#withholding_type').val()) || 0;
            const vatType = parseInt($('#vat_type').val());
            
            // หัก ณ จ่าย เฉพาะ Type 2 เท่านั้น
            if (vatType === 2) {
                const subTotal = calculateSubTotal();
                const discount = parseFloat($('#discount').val()) || 0;
                const afterDiscount = subTotal - discount;
                const vat = afterDiscount * 0.07;
                const totalWithVat = afterDiscount + vat;
                withholding.value = (totalWithVat * withholding_type).toFixed(2);
            } else {
                withholding.value = 0;
            }
            
            return withholding.value;
        };        // คำนวณราคารวม item.grand_total negative withholding_type
        const calculateGrandTotalMinusWithholding = () => {
            const vatType = parseInt($('#vat_type').val());
            const withholding_type = parseFloat($('#withholding_type').val()) || 0;
            const discount = parseFloat($('#discount').val()) || 0;
            
            let grandTotal = 0;
            for (let i = 0; i < orderreservationItems.value.length; i++) {
                grandTotal += calculateTotal(orderreservationItems.value[i]);
            }
            
            const afterDiscount = grandTotal - discount;
            if (vatType === 1) {
                // Type 1: ราคาสินค้ารวมภาษี (ไม่หัก ณ จ่าย)
                // ยอดรวมหลังหักภาษี = รวมจำนวนเงิน - ภาษีมูลค่าเพิ่ม
                const vat = afterDiscount * 0.07 / 1.07;
                return (afterDiscount - vat).toFixed(2);
            } else if (vatType === 2) {
                // Type 2: ราคาสินค้าแยกภาษี (หัก ณ จ่าย)
                const vat = afterDiscount * 0.07;
                const totalWithVat = afterDiscount + vat;
                const withholdingAmount = afterDiscount * withholding_type;
                const grandTotalMinusWithholding = totalWithVat - withholdingAmount;
                return grandTotalMinusWithholding.toFixed(2);
            } else {
                // Type 3: อัตราภาษี 0% (ไม่หัก ณ จ่าย)
                return afterDiscount.toFixed(2);
            }
        }
                  // Fetch productsorders by order
        const fetchProductsByOrders = (page = 1, search = '') => {
            var order_id = $('#order_id').val();
            currentPage.value = page;
            const offset = (page - 1) * itemsPerPage.value;
            let urlGetProductorder = $base_url + '/api/product-order.php?order_id=' + order_id + '&limit=' + itemsPerPage.value + '&offset=' + offset;
            let urlGetProductorderCount = $base_url + '/api/product-order.php?order_id=' + order_id + '&count_only=1';

            // Add search parameter if provided
            if (search && search.trim() !== '') {
                urlGetProductorder += '&search=' + encodeURIComponent(search);
                urlGetProductorderCount += '&search=' + encodeURIComponent(search);
            }

            if (order_id > 0) {
                // Get total count first
                $.get(urlGetProductorderCount, function(countData){
                    try {
                        const countResult = JSON.parse(countData);
                        totalProducts.value = countResult.total;
                        totalPages.value = Math.ceil(totalProducts.value / itemsPerPage.value);
                    } catch (error) {
                        console.error('Error parsing count data:', error);
                        totalProducts.value = 0;
                        totalPages.value = 0;
                    }
                });

                // Get products for current page
                $.get(urlGetProductorder, function(data){
                    try {
                        let productsData = JSON.parse(data);
                        
                        // Map the response to match your table structure
                        productsorders.value = productsData.map(product => ({
                            id: product.id || product.product_id,
                            product_id: product.product_id,
                            product_code: product.product_code,
                            product_name: product.product_name,
                            quantity: product.quantity || 1,
                            unit_name: product.unit_name,
                            store_id: product.store_id || '',
                            store_name: product.store_name || '-',
                            price: product.price,
                            selected: false
                        }));
                        selectAll.value = false;
                    } catch (e) {
                        console.error("Error parsing JSON response:", e);
                        Swal.fire({
                            title: 'Error',
                            text: 'Failed to load product data.',
                            icon: 'error'
                        });
                    }
                });
            } else {
                productsorders.value = [];
                totalProducts.value = 0;
                totalPages.value = 0;
            }
        };

        // Fetch orderreservation details
        const fetchOrdersReservationDetails = () => {
            const orderreservationId = new URLSearchParams(window.location.search).get('id');
            if (!orderreservationId) return;

            fetch(`${$base_url}/api/orders-reservation-detail.php?orders_reservation_id=${orderreservationId}`)
                .then(response => response.json())
                .then(data => {
                    orderreservationItems.value = data.map(item => ({
                        ...item,
                        pdf: item.profile_image ? `${$base_url}/upload_image/product/${item.profile_image}` : null
                    }));
                })
                .catch(error => console.error('Error fetching orderreservation details:', error));
        };



        // Remove an item from the orderreservation
        const removeOrderreservationItem = (index) => {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    orderreservationItems.value.splice(index, 1);
                    Swal.fire('Deleted!', 'Your item has been deleted.', 'success');
                }
            });
        };        const openProductModal = () => {
            fetchProductsByOrders();
            $('#productModal').modal('show');
        };

        // Search function
        const searchProducts = () => {
            const orderId = $('#order_id').val();
            if (orderId > 0) {
                fetchProductsByOrders(1, searchQuery.value);
            }
        };

        // Pagination functions
        const goToPage = (page) => {
            if (page >= 1 && page <= totalPages.value) {
                const orderId = $('#order_id').val();
                if (orderId > 0) {
                    fetchProductsByOrders(page, searchQuery.value);
                }
            }
        };

        const prevPage = () => {
            if (currentPage.value > 1) {
                goToPage(currentPage.value - 1);
            }
        };

        const nextPage = () => {
            if (currentPage.value < totalPages.value) {
                goToPage(currentPage.value + 1);
            }
        };

        // Select All functionality
        const toggleSelectAll = () => {
            productsorders.value.forEach(productorder => {
                productorder.selected = selectAll.value;
            });
        };

        const updateSelectAll = () => {
            const selectedCount = productsorders.value.filter(productorder => productorder.selected).length;
            const totalCount = productsorders.value.length;
            selectAll.value = selectedCount === totalCount && totalCount > 0;
        };        return {
            subTotal,
            vat,
            afterDiscount,
            discount,
            grandTotal,
            withholding,
            orderreservationItems,
            productsorders,
            documentDueDate,
            // Pagination variables
            currentPage,
            itemsPerPage,
            totalProducts,
            totalPages,
            selectAll,
            searchQuery,
            // Functions
            calculateGrandTotal,
            calculateWithholding,
            onSelectProductorder,
            calculateTotal,
            calculateSubTotal,
            calculateGrandTotalMinusWithholding,
            fetchProductsByOrders,
            fetchOrdersReservationDetails,
            removeOrderreservationItem,
            openProductModal,
            searchProducts,
            goToPage,
            prevPage,
            nextPage,
            toggleSelectAll,
            updateSelectAll
        };
    },    mounted() {
        //หน้า edit เท่าหน้า add
        if (window.location.search.includes('id=')) {
            this.fetchOrdersReservationDetails();
        }
        // ไม่ต้อง fetchProductsByOrders()  เพราะจะเมื่อ order_id
    },
    created() {
    }
}).mount('#appvue');

//## jquery section ##
$(function(){
    $('#supplier_id').on('change', function(){
        var supplier_id = $(this).val();
        var urlGetSupplier = $base_url + '/api/supplier.php?id=' + supplier_id;
        var urlOrder = $base_url + '/api/order.php?supplier_id=' + supplier_id;

        let supplier;
        $.get(urlGetSupplier).then((data) => {
            supplier = JSON.parse(data);
               const vatType = supplier.vat_type==1 ? 'Vat 7% (รวมภาษี)' : supplier.vat_type==2 ? 'Vat 7% (แยกภาษี)' : 'Vat 0%';
            $('#short_name').val(supplier.short_name);
            $('#customer_name').val(supplier.fullname);
            $('#contact_name').val(supplier.contact_name);
            $('#credit_day').val(supplier.credit_day);
            $('#payment_type').val(supplier.payment_type);
            $('#vat_type').val(supplier.vat_type);
            $('#vat_type_name').val(vatType);
            $('#withholding_type').val(supplier.withholding_type);

           // Calculate due date again when orderreservation is selected
           var document_date = $('#document_date').val();
           var credit_day = $('#credit_day').val();

           if (document_date && credit_day) {
               // Convert Thai date format (DD/MM/YYYY) to Date object
               const [day, month, year] = document_date.split('/');
               const dateObj = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

               // Add credit days
               dateObj.setDate(dateObj.getDate() + parseInt(credit_day || 0));

               // Format for display (DD/MM/YYYY)
               const formattedDay = String(dateObj.getDate()).padStart(2, '0');
               const formattedMonth = String(dateObj.getMonth() + 1).padStart(2, '0');
               const formattedYear = dateObj.getFullYear();
               const formattedDueDate = `${formattedDay}/${formattedMonth}/${formattedYear}`;

               // Set the input value and update Vue app
               $('#document_due_date').val(formattedDueDate);

               // Update the Vue data
               const vueApp = document.getElementById('appvue').__vue_app__;
               if (vueApp && vueApp._instance) {
                   const vueInstance = vueApp._instance.proxy;
                   if (vueInstance && vueInstance.documentDueDate !== undefined) {
                       vueInstance.documentDueDate = formattedDueDate;
                   }
               }
           }

            // Fetch orders after supplier data is loaded
            return $.get(urlOrder);
            }).then((data) => {
            let Orders = JSON.parse(data);
            $('#order_id').empty();
            $('#order_id').append('<option value="">-- เลือกใบส่งซื้อ --</option>');
                for (let i = 0; i < Orders.length; i++) {
                    $('#order_id').append('<option value="' + Orders[i].id + '">' + Orders[i].order_no + ' - ' + supplier.short_name + '</option>');
                }
            })
            .catch((error) => {
                console.error('Error fetching data:', error);
            });
    });

    // เมื่อเปลี่ยน order_id จะข้อมูลค้าและข้อมูลใบส่งซื้อ
    $('#order_id').on('change', function(){
        var order_id = $(this).val();
        
        if (order_id > 0) {
            // 1. ข้อมูลค้า modal
            var urlGetProductorder = $base_url + '/api/product-order.php?order_id=' + order_id;
            $.get(urlGetProductorder, function(data){
                try {
                    let products = JSON.parse(data);
                    for (let i = 0; i < products.length; i++) {
                        products[i].selected = false;
                    }

                    // Access the Vue instance properly
                    const vueApp = document.getElementById('appvue').__vue_app__;
                    if (vueApp && vueApp._instance) {
                        const vueInstance = vueApp._instance.proxy;
                        if (vueInstance && vueInstance.productsorders !== undefined) {
                            vueInstance.productsorders = products;
                        }
                    }
                } catch (e) {
                    console.error("Error parsing products data:", e);
                }
            }).fail(function() {
                console.error("Failed to fetch products data");
            });

            // 2. ข้อมูลใบส่งซื้อแสดง quotation_no
            var urlGetOrder = $base_url + '/api/order.php?id=' + order_id;
            $.get(urlGetOrder).then((data) => {
                let orders = JSON.parse(data);
                if (orders.length > 0) {
                    $('#quotation_no').val(orders[0].quotation_no);

                    // Calculate due date again when order is selected
                    var document_date = $('#document_date').val();
                    var credit_day = $('#credit_day').val();

                    if (document_date && credit_day) {
                        // Convert Thai date format (DD/MM/YYYY) to Date object
                        const [day, month, year] = document_date.split('/');
                        const dateObj = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

                        // Add credit days
                        dateObj.setDate(dateObj.getDate() + parseInt(credit_day || 0));

                        // Format for display (DD/MM/YYYY)
                        const formattedDay = String(dateObj.getDate()).padStart(2, '0');
                        const formattedMonth = String(dateObj.getMonth() + 1).padStart(2, '0');
                        const formattedYear = dateObj.getFullYear();
                        const formattedDueDate = `${formattedDay}/${formattedMonth}/${formattedYear}`;

                        // Set the input value and update Vue app
                        $('#document_due_date').val(formattedDueDate);

                        // Update the Vue data
                        const vueApp = document.getElementById('appvue').__vue_app__;
                        if (vueApp && vueApp._instance) {
                            const vueInstance = vueApp._instance.proxy;
                            if (vueInstance && vueInstance.documentDueDate !== undefined) {
                                vueInstance.documentDueDate = formattedDueDate;
                            }
                        }
                    }
                } else {
                    $('#quotation_no').val(''); // Clear the field if no data is found
                }
            }).catch((error) => {
                console.error('Error fetching order data:', error);
                $('#quotation_no').val(''); // Clear the field in case of an error
            });
        } else {
            // ถ้าไม่ได้ใบส่งซื้อ ให้ล้างข้อมูล
            $('#quotation_no').val('');
            const vueApp = document.getElementById('appvue').__vue_app__;
            if (vueApp && vueApp._instance) {
                const vueInstance = vueApp._instance.proxy;
                if (vueInstance && vueInstance.productsorders !== undefined) {
                    vueInstance.productsorders = [];
                }
            }
        }
    });    // เมื่อกดปุ่มลบในรายการ จะแจ้งข้อความ
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        let urlDelete = $(this).attr('href');
        Swal.fire({
            text: "การลบข้อมูล?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'ลบข้อมูล!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = urlDelete;
            }
        });
    });

    // ป้องกันการบันทึกข้อมูลเมื่อกด Enter ใน modal search
    $(document).on('keydown', '#productModal input[type="text"]', function(e) {
        if (e.which === 13) { // Enter key
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    });

    // ป้องกันการ submit form เมื่อกด Enter ใน productModal
    $(document).on('keydown', '#productModal', function(e) {
        if (e.which === 13) { // Enter key
            var target = e.target;
            // อนุญาตให้กด Enter เฉพาะปุ่ม "เลือกข้อมูล" เท่านั้น
            if (target.tagName !== 'BUTTON' || !target.classList.contains('btn-primary')) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
        }
    });
});
