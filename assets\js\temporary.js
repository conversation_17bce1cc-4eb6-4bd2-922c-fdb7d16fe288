const { createApp, ref } = Vue
const app = createApp({
    setup() {                
        const products = ref([]);
        const subTotal = ref(0);
        const discount = ref(0);
        const vat = ref(0);
        const withholding = ref(0);
        const afterDiscount = ref(0);
        const grandTotal = ref(0);
        const invoiceItems = ref([]);
        

        // เลือกสินค้าจาก modal
        const onSelectProduct = () => {
            for(let i = 0; i < products.value.length; i++) {
                if (products.value[i].selected) {
                    let qu = {
                        id: 0,
                        product_id: products.value[i].id,
                        product_code: products.value[i].product_code,
                        product_name: products.value[i].product_name,
                        quantity: products.value[i].quantity || 1, // Use quantity from reservation_detail if available
                        unit_name: products.value[i].unit_name,
                        price: products.value[i].price,
                        discount: 0,
                        total: products.value[i].price * (products.value[i].quantity || 1), // Calculate total based on quantity
                        pdf: products.value[i].profile_image ? $base_url + '/upload_image/product/' + products.value[i].profile_image : null,
                    };
                    invoiceItems.value.push(qu);
                }
            }

            $('#productModal').modal('hide');            
            products.value.forEach(product => {
                product.selected = false;
            });
        };

        // ลบรายการสินค้าในใบเสนอราคา
        const removeReservationItem = (index) => {
            Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
            if (result.isConfirmed) {
                invoiceItems.value.splice(index, 1);
                Swal.fire(
                'Deleted!',
                'Your item has been deleted.',
                'success'
                )
            }
            })
        }

        // คำนวณราคารวม item.total
        const calculateTotal = (item) => {
            let total = item.price * item.quantity;
            if (item.discount > 0) {
                total -= item.discount;
            }
            return total;
        }

        // คำนวณราคารวมทั้งหมด
        const calculateSubTotal = () => {
            let total = 0;
            for (let i = 0; i < invoiceItems.value.length; i++) {
                total += calculateTotal(invoiceItems.value[i]);
            }
            subTotal.value = total;
            return subTotal.value;
        }

        


        // คำนวณยอดรวมสุทธิ
        const calculateGrandTotal = () => {
            const discount = parseFloat($('#discount').val()) || 0;
            const vatType = parseInt($('#vat_type').val());
            const subTotal = calculateSubTotal();
            
            afterDiscount.value = (subTotal - discount).toFixed(2);
            
            if (vatType === 1) {
                vat.value = (afterDiscount.value * 0.07).toFixed(2);
            }

            // คำนวณยอดหัก ณ จ่าย
            const withholding_type = parseFloat($('#withholding_type').val()) || 0;
            withholding.value = (afterDiscount.value * withholding_type).toFixed(2);    

            // คำนวณยอดรวมทั้งหมด
            grandTotal.value = (
                parseFloat(afterDiscount.value) + 
                parseFloat(vat.value)
            ).toFixed(2);

            return grandTotal.value;
        };
            
        // คำนวณยอดหัก ณ จ่าย
            const calculateWithholding = () => {
                const withholding_type = parseFloat($('#withholding_type').val()) || 0;
                const subTotal = calculateSubTotal();
                withholding.value = (subTotal * withholding_type).toFixed(2);
                return withholding.value;
            };
            




           // คำนวณราคารวม item.grand_total negative withholding_type
        const calculateGrandTotalMinusWithholding = () => {
            let grandTotal = 0;
            for (let i = 0; i < invoiceItems.value.length; i++) {
                grandTotal += calculateTotal(invoiceItems.value[i]);
            }
            const withholding_type = parseFloat($('#withholding_type').val()) || 0;
            const withholdingAmount = grandTotal * withholding_type;
            const grandTotalMinusWithholding = grandTotal - withholdingAmount;
            return grandTotalMinusWithholding.toFixed(2);
        }
    

        
        

        const fetchProductsByReservation = (customer_id) => {
            var reservation_id = $('#reservation_id').val();
            var urlGetProduct = $base_url + '/api/product.php?reservation_id=' + reservation_id;

            if (reservation_id > 0) {
                $.get(urlGetProduct, function(data){
                    let products = JSON.parse(data);
                    for (let i = 0; i < products.length; i++) {
                        products[i].selected = false;
                    }
        
                    app.products = products;
                });
            }
        };

        // Fetch invoice details
        const fetchInvoiceDetails = () => {
            const invoiceId = new URLSearchParams(window.location.search).get('id');
            if (!invoiceId) return;

            fetch(`${$base_url}/api/invoice-detail.php?invoice_id=${invoiceId}`)
                .then(response => response.json())
                .then(data => {
                    invoiceItems.value = data.map(item => ({
                        ...item,
                        pdf: item.profile_image ? `${$base_url}/upload_image/product/${item.profile_image}` : null
                    }));
                })
                .catch(error => console.error('Error fetching invoice details:', error));
        };

        // Remove an item from the invoice
        const removeInvoiceItem = (index) => {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    invoiceItems.value.splice(index, 1);
                    Swal.fire('Deleted!', 'Your item has been deleted.', 'success');
                }
            });
        };

        return {
            subTotal,
            vat,
            afterDiscount,
            discount,
            grandTotal,
            withholding,
            invoiceItems,
            products,
            calculateGrandTotal,
            calculateWithholding,
            onSelectProduct,
            removeReservationItem,
            calculateTotal,
            calculateSubTotal,
            calculateGrandTotalMinusWithholding,
            fetchProductsByReservation,
            fetchInvoiceDetails,
            removeInvoiceItem
        }
    },
    mounted() {
        this.fetchInvoiceDetails();
        this.fetchProductsByReservation();

    }
}).mount('#appvue');

//## jquery section ##
$(function(){
    $('#customer_id').on('change', function(){
        var customer_id = $(this).val();        
        var urlGetCustomer = $base_url + '/api/customer.php?id=' + customer_id;
        var urlReservation = $base_url + '/api/reservation.php?customer_id=' + customer_id;      

        let customer;
        $.get(urlGetCustomer).then((data) => {
            customer = JSON.parse(data);
            const vatType = customer.vat_type == 1 ? 'Vat 7%' : 'Vat 0%';
            $('#short_name').val(customer.short_name);
            $('#customer_name').val(customer.fullname);
            $('#contact_name').val(customer.contact_name);
            $('#credit_day').val(customer.credit_day);
            $('#payment_type').val(customer.payment_type);
            $('#vat_type').val(customer.vat_type);
            $('#vat_type_name').val(vatType);
            $('#withholding_type').val(customer.withholding_type);

            

            // Fetch reservations after customer data is loaded
            return $.get(urlReservation);
            }).then((data) => {
            let reservations = JSON.parse(data);
            $('#reservation_id').empty();
            $('#reservation_id').append('<option value="">-- เลือกใบจอง PO --</option>');
                for (let i = 0; i < reservations.length; i++) {
                    $('#reservation_id').append('<option value="' + reservations[i].id + '">' + reservations[i].document_number + ' - ' + customer.short_name + '</option>');
                }
            })
            .catch((error) => {
                console.error('Error fetching data:', error);
            });
    });
    
    $('#reservation_id').on('change', function(){        
        var reservation_id = $(this).val(); 
        var urlGetProduct = $base_url + '/api/product.php?reservation_id=' + reservation_id;

        if (reservation_id > 0) {
            $.get(urlGetProduct, function(data){
                let products = JSON.parse(data);
                for (let i = 0; i < products.length; i++) {
                    products[i].selected = false;
                }
    
                app.products = products;
            });
        }
    });

    //เมื่อ reservation_id จะ ข้อมูล purchase_order มาแสดงใน
    $('#reservation_id').on('change', function() {
        var reservation_id = $(this).val();
        var urlGetReservation = $base_url + '/api/reservation.php?id=' + reservation_id;
    
        $.get(urlGetReservation).then((data) => {
            let reservations = JSON.parse(data);
            if (reservations.length > 0) {
                $('#purchase_order').val(reservations[0].purchase_order);
            } else {
                $('#purchase_order').val(''); // Clear the field if no data is found
            }
        }).catch((error) => {
            console.error('Error fetching reservation data:', error);
            $('#purchase_order').val(''); // Clear the field in case of an error
        });
    });


    
    // เมื่อกดปุ่มลบในรายการ จะแจ้งข้อความ
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        let urlDelete = $(this).attr('href');
        Swal.fire({            
            text: "การลบข้อมูล?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'ลบข้อมูล!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = urlDelete;
            }
        });
    });
});
