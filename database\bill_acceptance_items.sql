-- สคีมาสำหรับระบบบัญชีเจ้าหนี้ (Account Payable System)



-- ตารางสำหรับรายการสินค้าในใบรับวางบิล
CREATE TABLE `bill_acceptance_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bill_acceptance_id` int(11) NOT NULL COMMENT 'รหัสใบรับวางบิล',
  `product_code` varchar(50) DEFAULT NULL COMMENT 'รหัสสินค้า',
  `product_name` varchar(255) NOT NULL COMMENT 'ชื่อสินค้า/บริการ',
  `description` text DEFAULT NULL COMMENT 'รายละเอียด',
  `quantity` decimal(10,3) NOT NULL DEFAULT 1.000 COMMENT 'จำนวน',
  `unit` varchar(20) DEFAULT 'ชิ้น' COMMENT 'หน่วยนับ',
  `unit_price` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'ราคาต่อหน่วย',
  `amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงิน',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_bill_acceptance_id` (`bill_acceptance_id`),
  KEY `idx_product_code` (`product_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

