<?php return array(
    'root' => array(
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => NULL,
        'name' => '__root__',
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => NULL,
            'dev_requirement' => false,
        ),
        'mpdf/mpdf' => array(
            'pretty_version' => 'v8.0.13',
            'version' => '8.0.13.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/mpdf',
            'aliases' => array(),
            'reference' => '42f145615cfe830fd432474da1d2e1f927efe402',
            'dev_requirement' => false,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.10.2',
            'version' => '1.10.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'reference' => '776f831124e9c62e1a2c601ecc52e776d8bb7220',
            'dev_requirement' => false,
            'replaced' => array(
                0 => '1.10.2',
            ),
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'dev_requirement' => false,
        ),
        'setasign/fpdi' => array(
            'pretty_version' => 'v2.3.6',
            'version' => '2.3.6.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../setasign/fpdi',
            'aliases' => array(),
            'reference' => '6231e315f73e4f62d72b73f3d6d78ff0eed93c31',
            'dev_requirement' => false,
        ),
    ),
);
