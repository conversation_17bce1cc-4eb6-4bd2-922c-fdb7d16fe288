const { createApp, ref } = Vue
const app = createApp({
    setup() {          const products = ref([]);
        const units = ref([]);
        const subTotal = ref(0);
        const discount = ref(0);
        const vat = ref(0);
        const afterDiscount = ref(0);
        const grandTotal = ref(0);
        const orderItems = ref([]);
        
        // Pagination variables
        const currentPage = ref(1);
        const itemsPerPage = ref(10);
        const totalProducts = ref(0);
        const totalPages = ref(0);
        const selectAll = ref(false);
        const searchQuery = ref('');

        // เลือกสินค้าจาก modal
        const onSelectProduct = () => {
            for (let i = 0; i < products.value.length; i++) {
                if (products.value[i].selected) {
                    let qu = {
                        id: 0,
                        product_id: products.value[i].id,
                        product_code: products.value[i].product_code,
                        product_name: products.value[i].product_name,
                        quantity: products.value[i].quantity || 1, // Use quantity from requisition_detail if available
                        unit_name: products.value[i].unit_name,
                        price: products.value[i].price,
                        discount: 0,
                        total: products.value[i].price * (products.value[i].quantity || 1), // Calculate total based on quantity
                        pdf: products.value[i].profile_image ? $base_url + '/upload_image/product/' + products.value[i].profile_image : null,
                    };
                    orderItems.value.push(qu);
                }
            }

            $('#productModal').modal('hide');
            products.value.forEach(product => {
                product.selected = false;
            });

        };

        // ลบรายการสินค้าในใบขอซัพพลายเออร์
        const removeRequisitionItem = (index) => {
            Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
            if (result.isConfirmed) {
                orderItems.value.splice(index, 1);
                Swal.fire(
                'Deleted!',
                'Your item has been deleted.',
                'success'
                )
            }
            })
        }
        // คำนวณราคารวม item.total
        const calculateTotal = (item) => {
            let total = item.price * item.quantity;
            if (item.discount > 0) {
                total -= item.discount;
            }
            return total;
        }

        // คำนวณราคารวมทั้งหมด
        const calculateSubTotal = () => {
            let total = 0;
            for (let i = 0; i < orderItems.value.length; i++) {
                total += calculateTotal(orderItems.value[i]);
            }
            subTotal.value = total;
            return subTotal.value;
        }

        // คำนวณยอดรวมสุทธิ
        const calculateGrandTotal = () => {
            const discount = parseFloat($('#discount').val()) || 0;
            const vatType = parseInt($('#vat_type').val());
            const subTotal = calculateSubTotal();
            afterDiscount.value = (subTotal - discount).toFixed(2);
                        
            if (vatType === 1) {
                // VAT 7% รวมภาษี - VAT is already included in the price
                // Calculate VAT amount from total (reverse calculation)
                vat.value = (afterDiscount.value * 0.07 / 1.07).toFixed(2);
                grandTotal.value = parseFloat(afterDiscount.value).toFixed(2);
            } else if (vatType === 2) {
                // VAT 7% แยกภาษี - VAT is separate from price
                vat.value = (afterDiscount.value * 0.07).toFixed(2);
                grandTotal.value = (parseFloat(afterDiscount.value) + parseFloat(vat.value)).toFixed(2);
            } else if (vatType === 3) {
                // VAT 0% - No VAT
                vat.value = 0;
                grandTotal.value = parseFloat(afterDiscount.value).toFixed(2);
            } else {
                // Default case - No VAT
                vat.value = 0;
                grandTotal.value = parseFloat(afterDiscount.value).toFixed(2);
            }

            return grandTotal.value;
        }
        
        const fetchProductsBySupplier = (supplier_id, page = 1, search = '') => {
            currentPage.value = page;
            const offset = (page - 1) * itemsPerPage.value;
            const requisition_id = $('#requisition_id').val();
            let urlGetProduct = $base_url + '/api/product-order.php?supplier_id=' + supplier_id + '&requisition_id=' + requisition_id + '&limit=' + itemsPerPage.value + '&offset=' + offset;
            let urlGetProductCount = $base_url + '/api/product-order.php?supplier_id=' + supplier_id + '&requisition_id=' + requisition_id + '&count_only=1';
            
            // Add search parameter if provided
            if (search && search.trim() !== '') {
                urlGetProduct += '&search=' + encodeURIComponent(search);
                urlGetProductCount += '&search=' + encodeURIComponent(search);
            }
            
            // Get total count first
            $.get(urlGetProductCount, function(countData) {
                try {
                    const countResult = JSON.parse(countData);
                    totalProducts.value = countResult.total;
                    totalPages.value = Math.ceil(totalProducts.value / itemsPerPage.value);
                } catch (e) {
                    console.error("Error parsing count JSON:", e);
                }
            });
            
            // Get products for current page
            $.get(urlGetProduct, function(data) {
                try {
                    let productsData = JSON.parse(data);
                    for (let i = 0; i < productsData.length; i++) {
                        productsData[i].selected = false;
                        // ใช้ quantity จาก requisition ถ้ามี
                        productsData[i].quantity = productsData[i].requisition_quantity || 1;
                    }
                    products.value = productsData;
                    selectAll.value = false;
                } catch (e) {
                    console.error("Error parsing JSON response:", e);
                    console.log("Raw response:", data);
                    // Show error to user
                    Swal.fire({
                        title: 'Error',
                        text: 'Failed to load product data. Please try again or contact support.',
                        icon: 'error'
                    });
                }
            }).fail(function(jqXHR, textStatus, errorThrown) {
                console.error("AJAX request failed:", textStatus, errorThrown);
                Swal.fire({
                    title: 'Error',
                    text: 'Failed to connect to the server. Please check your connection and try again.',
                    icon: 'error'
                });
            });
        };        // Fetch units for dropdown
        const fetchUnits = () => {
            const urlGetUnits = $base_url + '/api/units.php';
            $.get(urlGetUnits, function(data) {
                try {
                    let unitsData = JSON.parse(data);
                    units.value = unitsData;
                    console.log('Units loaded:', units.value);
                } catch (e) {
                    console.error("Error parsing units JSON:", e);
                    console.log("Raw response:", data);
                    Swal.fire({
                        title: 'Error',
                        text: 'Failed to load units data. Please try again or contact support.',
                        icon: 'error'
                    });
                }
            }).fail(function(jqXHR, textStatus, errorThrown) {
                console.error("AJAX request failed:", textStatus, errorThrown);
                Swal.fire({
                    title: 'Error',
                    text: 'Failed to connect to the server. Please check your connection and try again.',
                    icon: 'error'
                });
            });
        };

        // Fetch order details
        const fetchOrderDetails = () => {
            const orderId = new URLSearchParams(window.location.search).get('id');
            if (!orderId) {
                console.log('No order ID found in URL');
                return;
            }

            fetch(`${$base_url}/api/order-detail.php?order_id=${orderId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Check if the response contains an error
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    
                    // Map the data and add the pdf path
                    orderItems.value = data.map(item => ({
                        ...item,
                        pdf: item.profile_image ? `${$base_url}/upload_image/product/${item.profile_image}` : null
                    }));
                    
                    console.log('Order details loaded:', orderItems.value);
                })
                .catch(error => {
                    console.error('Error fetching order details:', error);
                    Swal.fire({
                        title: 'Error',
                        text: 'Failed to load order details. Please try again or contact support.',
                        icon: 'error'
                    });
                });
        };

        // Remove an item from the order
        const removeOrderItem = (index) => {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    orderItems.value.splice(index, 1);
                    Swal.fire('Deleted!', 'Your item has been deleted.', 'success');
                }
            });
        };

        // Search function
        const searchProducts = () => {
            const supplierId = $('#supplier_id').val();
            if (supplierId > 0) {
                fetchProductsBySupplier(supplierId, 1, searchQuery.value);
            }
        };

        // Pagination functions
        const goToPage = (page) => {
            if (page >= 1 && page <= totalPages.value) {
                const supplierId = $('#supplier_id').val();
                if (supplierId > 0) {
                    fetchProductsBySupplier(supplierId, page, searchQuery.value);
                }
            }
        };

        const prevPage = () => {
            if (currentPage.value > 1) {
                goToPage(currentPage.value - 1);
            }
        };

        const nextPage = () => {
            if (currentPage.value < totalPages.value) {
                goToPage(currentPage.value + 1);
            }
        };

        // Select All functionality
        const toggleSelectAll = () => {
            products.value.forEach(product => {
                product.selected = selectAll.value;
            });
        };

        const updateSelectAll = () => {
            const selectedCount = products.value.filter(product => product.selected).length;
            const totalCount = products.value.length;
            selectAll.value = selectedCount === totalCount && totalCount > 0;
        };        return {
            subTotal,
            vat,
            afterDiscount,
            discount,
            grandTotal,
            orderItems,
            products,
            units,
            calculateGrandTotal,
            onSelectProduct,
            removeOrderItem,
            removeRequisitionItem,
            calculateTotal,
            calculateSubTotal,
            fetchProductsBySupplier,
            fetchOrderDetails,
            fetchUnits,
            // Pagination
            currentPage,
            itemsPerPage,
            totalProducts,
            totalPages,
            goToPage,
            prevPage,
            nextPage,
            // Select All
            selectAll,
            toggleSelectAll,
            updateSelectAll,
            // Search
            searchQuery,
            searchProducts
        }
    },    mounted() {
        this.fetchOrderDetails();
        // Initialize existing values from the form
        this.discount.value = parseFloat($('#discount').val()) || 0;
        this.calculateGrandTotal();
        
        // Only fetch products if a supplier is selected
        const supplierId = $('#supplier_id').val();
        if (supplierId) {
            this.fetchProductsBySupplier(supplierId, 1);
        }
    },
    created() {
        this.fetchOrderDetails();
    }
    
}).mount('#appvue');

//## jquery section ##
$(function(){
    $('#supplier_id').on('change', function(){
        var supplier_id = $(this).val();
        var urlGetSupplier = $base_url + '/api/supplier.php?id=' + supplier_id;
        var urlRequisition = $base_url + '/api/requisition.php?supplier_id=' + supplier_id; // Changed from requisitions.php to requisition.php

        let supplier;        $.get(urlGetSupplier).then((data) => {
            try {
                supplier = JSON.parse(data);
                 const vatType = supplier.vat_type==1 ? 'Vat 7% (รวมภาษี)' : supplier.vat_type==2 ? 'Vat 7% (แยกภาษี)' : 'Vat 0%';
                $('#short_name').val(supplier.short_name);
                $('#supplier_name').val(supplier.fullname);
                $('#contact_name').val(supplier.contact_name);
                $('#credit_day').val(supplier.credit_day);
                $('#payment_type').val(supplier.payment_type);
                $('#vat_type').val(supplier.vat_type);
                $('#vat_type_name').val(vatType);

                // Fetch requisition after supplier data is loaded
                return $.get(urlRequisition);
            } catch (e) {
                console.error("Error parsing supplier JSON:", e);
                console.log("Raw response:", data);
                Swal.fire({
                    title: 'Error',
                    text: 'Failed to load supplier data. Please try again or contact support.',
                    icon: 'error'
                });
                throw e; // Rethrow to stop the promise chain
            }
        }).then((data) => {
            try {
                let requisition = JSON.parse(data);
                $('#requisition_id').empty();
                $('#requisition_id').append('<option value="">-- เลือกใบขอซื้อ --</option>');
                for (let i = 0; i < requisition.length; i++) {
                    $('#requisition_id').append('<option value="' + requisition[i].id + '">' + requisition[i].document_no + ' - ' + supplier.short_name + '</option>');
                }
            } catch (e) {
                console.error("Error parsing requisition JSON:", e);
                console.log("Raw response:", data);
                Swal.fire({
                    title: 'Error',
                    text: 'Failed to load requisition data. Please try again or contact support.',
                    icon: 'error'
                });
            }
        })
        .catch((error) => {
            console.error('Error fetching data:', error);
            Swal.fire({
                title: 'Error',
                text: 'Failed to connect to the server. Please check your connection and try again.',
                icon: 'error'
            });
        });
    });

    $('#requisition_id').on('change', function(){
        var requisition_id = $(this).val();
        var urlGetProduct = $base_url + '/api/product-order.php?requisition_id=' + requisition_id;

        if (requisition_id > 0) {
            $.get(urlGetProduct, function(data){
                try {
                    let products = JSON.parse(data);
                    for (let i = 0; i < products.length; i++) {
                        products[i].selected = false;
                    }

                    app.products = products;
                } catch (e) {
                    console.error("Error parsing JSON response:", e);
                    console.log("Raw response:", data);
                    // Show error to user
                    Swal.fire({
                        title: 'Error',
                        text: 'Failed to load product data. Please try again or contact support.',
                        icon: 'error'
                    });
                }
            }).fail(function(_, textStatus, errorThrown) {
                console.error("AJAX request failed:", textStatus, errorThrown);
                Swal.fire({
                    title: 'Error',
                    text: 'Failed to connect to the server. Please check your connection and try again.',
                    icon: 'error'
                });
            });
        }
    });    // เมื่อกดปุ่มลบในรายการ จะแจ้งข้อความ
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        let urlDelete = $(this).attr('href');
        Swal.fire({
            text: "การลบข้อมูล?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'ลบข้อมูล!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = urlDelete;
            }
        });
    });

    // ป้องกันการบันทึกข้อมูลเมื่อกด Enter ใน modal search
    $(document).on('keydown', '#productModal input[type="text"]', function(e) {
        if (e.which === 13) { // Enter key
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    });

    // ป้องกันการ submit form เมื่อกด Enter ใน productModal
    $(document).on('keydown', '#productModal', function(e) {
        if (e.which === 13) { // Enter key
            var target = e.target;
            // อนุญาตให้กด Enter เฉพาะปุ่ม "เลือกข้อมูล" เท่านั้น
            if (target.tagName !== 'BUTTON' || !target.classList.contains('btn-primary')) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
        }
    });
});
