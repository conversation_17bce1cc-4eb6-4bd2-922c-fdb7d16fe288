//ลบสินค้า และฟังก์ชันเสริมสำหรับ product list

// ฟังก์ชันแสดงข้อมูลการนำทาง
function showNavigationInfo() {
    const urlParams = new URLSearchParams(window.location.search);
    const currentPage = parseInt(urlParams.get('page')) || 1;
    console.log('หน้าปัจจุบัน:', currentPage);
    console.log('คีย์บอร์ดช็อตคัท:');
    console.log('- Ctrl + ← : หน้าก่อนหน้า');
    console.log('- Ctrl + → : หน้าถัดไป');
    console.log('- Ctrl + Home : หน้าแรก');
    console.log('- Ctrl + End : หน้าสุดท้าย');
}

// ฟังก์ชันเสริมสำหรับการจัดการ URL parameters
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// ฟังก์ชันสำหรับอัปเดต URL parameters
function updateUrlParameter(param, value) {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set(param, value);
    return window.location.pathname + '?' + urlParams.toString();
}

// ฟังก์ชันสำหรับลบ URL parameter
function removeUrlParameter(param) {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.delete(param);
    return window.location.pathname + '?' + urlParams.toString();
}

// เมื่อโหลดหน้าเสร็จ แสดงข้อมูลการนำทาง
document.addEventListener('DOMContentLoaded', function() {
    console.log('Product.js loaded');
    // แสดงข้อมูลการนำทาง (ไม่บังคับ)
    // showNavigationInfo();
});

