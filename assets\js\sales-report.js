// Sales Report JavaScript Functions - Enhanced for Sales Management System
function openReportPopup() {
    var reportModal = new bootstrap.Modal(document.getElementById('reportModal'));
    
    // Initialize form when popup opens
    initializeForm();
    
    // Show modal
    reportModal.show();
    
    // Ensure Select2 is initialized after modal is fully shown
    $('#reportModal').on('shown.bs.modal', function (e) {
        setTimeout(function() {
            console.log('Modal shown, initializing Select2...');
            initializeSelect2();
        }, 150);
    });
    
    // Clean up event listeners to prevent multiple bindings
    $('#reportModal').off('hidden.bs.modal.select2cleanup');
    $('#reportModal').on('hidden.bs.modal.select2cleanup', function (e) {
        if (typeof $ !== 'undefined' && $('#customerFilter').hasClass('select2-hidden-accessible')) {
            $('#customerFilter').select2('destroy');
        }
    });
}

function initializeForm() {
    // Set default date range for current month
    updateDateRange();
    
    // Initialize Select2 for customer dropdown
    initializeSelect2();
    
    // Set up event listeners for document filter checkbox
    const enableDocFilter = document.getElementById('enableDocFilter');
    const docRangeRow = document.getElementById('docRangeRow');
    
    if (enableDocFilter && docRangeRow) {
        enableDocFilter.addEventListener('change', function() {
            if (this.checked) {
                docRangeRow.style.display = 'block';
                docRangeRow.classList.add('show');
                docRangeRow.classList.remove('hide');
                
                setTimeout(() => {
                    docRangeRow.style.opacity = '1';
                    docRangeRow.style.maxHeight = '200px';
                }, 10);
            } else {
                docRangeRow.classList.add('hide');
                docRangeRow.classList.remove('show');
                docRangeRow.style.opacity = '0';
                docRangeRow.style.maxHeight = '0';
                
                setTimeout(() => {
                    docRangeRow.style.display = 'none';
                }, 300);
                
                // Clear values
                const docStart = document.getElementById('docStart');
                const docEnd = document.getElementById('docEnd');
                if (docStart) docStart.value = '';
                if (docEnd) docEnd.value = '';
            }
        });
    }
    
    // Set up event listeners for void document filters
    const includeVoidedDocs = document.getElementById('includeVoidedDocs');
    const onlyVoidedDocs = document.getElementById('onlyVoidedDocs');
    
    if (includeVoidedDocs && onlyVoidedDocs) {
        includeVoidedDocs.addEventListener('change', function() {
            if (this.checked) {
                onlyVoidedDocs.checked = false;
            }
        });
        
        onlyVoidedDocs.addEventListener('change', function() {
            if (this.checked) {
                includeVoidedDocs.checked = false;
                // Auto-enable void reason display
                const showVoidedReason = document.getElementById('showVoidedReason');
                if (showVoidedReason) {
                    showVoidedReason.checked = true;
                }
            }
        });
    }
    
    // Initialize tooltips
    initializeTooltips();
}

function initializeTooltips() {
    // Add tooltips to buttons and form elements
    const tooltips = {
        'reportDate': 'วันที่ที่จะแสดงในรายงาน',
        'reportUser': 'ชื่อผู้ออกรายงาน',
        'reportFormat': 'รูปแบบการแสดงผลรายงาน',
        'periodType': 'เลือกช่วงเวลาที่ต้องการ',
        'customerFilter': 'กรองรายงานตามลูกค้า',
        'statusFilter': 'กรองรายงานตามสถานะ',
        'includeVoidedDocs': 'รวมเอกสารที่ยกเลิกแล้วในรายงาน',
        'onlyVoidedDocs': 'แสดงเฉพาะเอกสารที่ยกเลิกแล้ว',
        'showVoidedReason': 'แสดงเหตุผลการยกเลิกในรายงาน',
        'showProductDetails': 'แสดงรายละเอียดสินค้าในรายงาน',
        'showCustomerDetails': 'แสดงรายละเอียดลูกค้าในรายงาน',
        'showTotalSummary': 'แสดงสรุปยอดรวมในรายงาน',
        'groupByCustomer': 'จัดกลุ่มรายงานตามลูกค้า'
    };
    
    Object.entries(tooltips).forEach(([id, title]) => {
        const element = document.getElementById(id);
        if (element) {
            element.title = title;
            element.setAttribute('data-bs-toggle', 'tooltip');
            element.setAttribute('data-bs-placement', 'top');
        }
    });
    
    // Initialize Bootstrap tooltips
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

function initializeSelect2() {
    // Wait for DOM and libraries to be ready
    if (typeof $ === 'undefined') {
        console.error('jQuery is not loaded');
        return;
    }
    
    if (typeof $.fn.select2 === 'undefined') {
        console.error('Select2 is not loaded');
        return;
    }

    try {
        // Initialize customer filter select2
        $('#customerFilter').select2({
            placeholder: 'เลือกลูกค้า...',
            allowClear: true,
            dropdownParent: $('#reportModal'),
            language: {
                noResults: function() {
                    return "ไม่พบข้อมูลลูกค้า";
                },
                searching: function() {
                    return "กำลังค้นหา...";
                },
                loadingMore: function() {
                    return "กำลังโหลดข้อมูลเพิ่มเติม...";
                }
            },
            escapeMarkup: function(markup) {
                return markup;
            }
        });
        
        console.log('Select2 initialized successfully');
    } catch (error) {
        console.error('Error initializing Select2:', error);
    }
}

function validateForm() {
    const dateStart = document.getElementById('dateStart').value;
    const dateEnd = document.getElementById('dateEnd').value;
    const enableDocFilter = document.getElementById('enableDocFilter').checked;
    const docStart = document.getElementById('docStart').value;
    const docEnd = document.getElementById('docEnd').value;
    
    // Validate date range
    if (!dateStart || !dateEnd) {
        showAlert('กรุณาระบุช่วงวันที่', 'warning');
        return false;
    }
    
    if (new Date(dateStart) > new Date(dateEnd)) {
        showAlert('วันที่เริ่มต้นต้องไม่มากกว่าวันที่สิ้นสุด', 'warning');
        return false;
    }
    
    // Check if date range is too wide (more than 1 year)
    const daysDiff = (new Date(dateEnd) - new Date(dateStart)) / (1000 * 60 * 60 * 24);
    if (daysDiff > 365) {
        if (!confirm('ช่วงวันที่ที่เลือกมากกว่า 1 ปี ซึ่งอาจทำให้รายงานใช้เวลานาน ต้องการดำเนินการต่อหรือไม่?')) {
            return false;
        }
    }
    
    // Validate document range if enabled
    if (enableDocFilter) {
        if (!docStart || !docEnd) {
            showAlert('กรุณาระบุช่วงเลขที่เอกสาร', 'warning');
            return false;
        }
        
        if (docStart > docEnd) {
            showAlert('เลขที่เอกสารเริ่มต้นต้องไม่มากกว่าเลขที่เอกสารสิ้นสุด', 'warning');
            return false;
        }
    }
    
    return true;
}

function getFormData() {
    // Get customer filter value (handle both regular select and Select2)
    let customerFilterValue = '';
    const customerFilterElement = document.getElementById('customerFilter');
    
    if (customerFilterElement) {
        if (typeof $ !== 'undefined' && $('#customerFilter').hasClass('select2-hidden-accessible')) {
            customerFilterValue = $('#customerFilter').val();
        } else {
            customerFilterValue = customerFilterElement.value;
        }
    }
    
    // Get void option from radio buttons
    const voidOption = document.querySelector('input[name="voidOption"]:checked')?.value || 'exclude';
    
    return {
        reportDate: document.getElementById('reportDate')?.value || '',
        reportUser: document.getElementById('reportUser')?.value || '',
        reportFormat: document.getElementById('reportFormat')?.value || 'detailed',
        dateStart: document.getElementById('dateStart')?.value || '',
        dateEnd: document.getElementById('dateEnd')?.value || '',
        docStart: document.getElementById('enableDocFilter')?.checked ? (document.getElementById('docStart')?.value || '') : '',
        docEnd: document.getElementById('enableDocFilter')?.checked ? (document.getElementById('docEnd')?.value || '') : '',
        customerFilter: customerFilterValue,
        statusFilter: document.getElementById('statusFilter')?.value || '',
        voidOption: voidOption,
        includeVoidedDocs: voidOption === 'include',
        onlyVoidedDocs: voidOption === 'only',
        showProductDetails: document.getElementById('showProductDetails')?.checked || false,
        showCustomerDetails: document.getElementById('showCustomerDetails')?.checked || false,
        showTotalSummary: document.getElementById('showTotalSummary')?.checked || false,
        groupByCustomer: document.getElementById('groupByCustomer')?.checked || false,
        showVoidedReason: document.getElementById('showVoidedReason')?.checked || false,
        showPaymentStatus: document.getElementById('showPaymentStatus')?.checked || false
    };
}

function printReport(type) {
    if (!validateForm()) {
        return;
    }
    
    const formData = getFormData();
    
    // Build query string
    const params = new URLSearchParams({
        type: type,
        start: formData.dateStart,
        end: formData.dateEnd,
        format: formData.reportFormat,
        customer_id: formData.customerFilter,
        status: formData.statusFilter,
        void_option: formData.voidOption,
        show_products: formData.showProductDetails ? '1' : '0',
        show_customers: formData.showCustomerDetails ? '1' : '0',
        show_summary: formData.showTotalSummary ? '1' : '0',
        group_by_customer: formData.groupByCustomer ? '1' : '0',
        show_voided_reason: formData.showVoidedReason ? '1' : '0',
        show_payment_status: formData.showPaymentStatus ? '1' : '0'
    });
    
    // Add document range if specified
    if (formData.docStart) {
        params.append('doc_start', formData.docStart);
    }
    if (formData.docEnd) {
        params.append('doc_end', formData.docEnd);
    }
    
    // Show loading indicator
    showLoading(true);
    
    // Handle different report types - THIS IS THE KEY CHANGE FOR SALES REPORTS
    const url = `report-sales-generate.php?${params.toString()}`;
    
    switch(type) {
        case 'print':
            window.open(url, '_blank');
            break;
        case 'text':
            window.open(url + '&output=text', '_blank');
            break;
        case 'excel':
            // For Excel download, create a temporary form
            const form = document.createElement('form');
            form.method = 'GET';
            form.action = url + '&output=excel';
            form.style.display = 'none';
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
            break;
        case 'pdf':
            window.open(url + '&output=pdf', '_blank');
            break;
        case 'edit':
            // Open edit mode
            const editUrl = url.replace('report-sales-generate.php', 'report-sales-edit.php');
            window.open(editUrl, '_blank');
            break;
        default:
            window.open(url, '_blank');
    }
    
    // Hide loading indicator after a short delay
    setTimeout(() => showLoading(false), 1000);
}

function previewReport() {
    if (!validateForm()) {
        return;
    }
    
    const formData = getFormData();
    const params = new URLSearchParams({
        type: 'preview',
        start: formData.dateStart,
        end: formData.dateEnd,
        format: formData.reportFormat,
        customer_id: formData.customerFilter,
        status: formData.statusFilter,
        include_voided: formData.includeVoidedDocs ? '1' : '0',
        only_voided: formData.onlyVoidedDocs ? '1' : '0',
        show_products: formData.showProductDetails ? '1' : '0',
        show_customers: formData.showCustomerDetails ? '1' : '0',
        show_summary: formData.showTotalSummary ? '1' : '0',
        group_by_customer: formData.groupByCustomer ? '1' : '0',
        show_voided_reason: formData.showVoidedReason ? '1' : '0'
    });
    
    if (formData.docStart) params.append('doc_start', formData.docStart);
    if (formData.docEnd) params.append('doc_end', formData.docEnd);
    
    window.open(`report-sales-generate.php?${params.toString()}`, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
}

function downloadFile(url) {
    const link = document.createElement('a');
    link.href = url;
    link.download = '';
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function showLoading(show) {
    // Show/hide loading indicator on buttons
    const buttons = document.querySelectorAll('.modal-footer button');
    buttons.forEach(button => {
        button.disabled = show;
        if (show) {
            // Store original text and show loading
            if (!button.dataset.originalText) {
                button.dataset.originalText = button.innerHTML;
            }
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status"></span>กำลังประมวลผล...';
        } else {
            // Restore original text
            if (button.dataset.originalText) {
                button.innerHTML = button.dataset.originalText;
            }
        }
    });
}

function setupPrinter() {
    // Open printer setup dialog
    if (window.print) {
        showAlert('กรุณาเลือก "Print to PDF" หรือเครื่องพิมพ์ที่ต้องการในหน้าต่างที่เปิดขึ้น', 'info');
        setTimeout(() => {
            window.print();
        }, 1000);
    } else {
        showAlert('ฟังก์ชันพิมพ์ไม่รองรับในเบราว์เซอร์นี้', 'error');
    }
}

function updateDateRange() {
    const periodType = document.getElementById('periodType')?.value;
    if (!periodType) return;
    
    const today = new Date();
    let startDate = new Date();
    let endDate = new Date();
    
    switch(periodType) {
        case 'today':
            startDate = new Date(today);
            endDate = new Date(today);
            break;
        case 'yesterday':
            startDate = new Date(today.getTime() - 24 * 60 * 60 * 1000);
            endDate = new Date(today.getTime() - 24 * 60 * 60 * 1000);
            break;
        case 'this_week':
            const dayOfWeek = today.getDay();
            const diff = today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1);
            startDate = new Date(today);
            startDate.setDate(diff);
            endDate = new Date(startDate.getTime() + 6 * 24 * 60 * 60 * 1000);
            break;
        case 'last_week':
            const lastWeekStart = new Date(today);
            lastWeekStart.setDate(today.getDate() - today.getDay() - 6);
            startDate = lastWeekStart;
            endDate = new Date(lastWeekStart.getTime() + 6 * 24 * 60 * 60 * 1000);
            break;
        case 'month':
            startDate = new Date(today.getFullYear(), today.getMonth(), 1);
            endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            break;
        case 'last_month':
            startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            endDate = new Date(today.getFullYear(), today.getMonth(), 0);
            break;
        case 'quarter':
            const quarter = Math.floor(today.getMonth() / 3);
            startDate = new Date(today.getFullYear(), quarter * 3, 1);
            endDate = new Date(today.getFullYear(), quarter * 3 + 3, 0);
            break;
        case 'year':
            startDate = new Date(today.getFullYear(), 0, 1);
            endDate = new Date(today.getFullYear(), 11, 31);
            break;
        default:
            // Custom - don't change dates
            return;
    }
    
    const dateStartElement = document.getElementById('dateStart');
    const dateEndElement = document.getElementById('dateEnd');
    
    if (dateStartElement) {
        dateStartElement.value = startDate.toISOString().split('T')[0];
    }
    if (dateEndElement) {
        dateEndElement.value = endDate.toISOString().split('T')[0];
    }
}

function showAlert(message, type = 'info') {
    // Create or update alert element
    let alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alertContainer';
        alertContainer.style.position = 'fixed';
        alertContainer.style.top = '20px';
        alertContainer.style.right = '20px';
        alertContainer.style.zIndex = '9999';
        document.body.appendChild(alertContainer);
    }
    
    const alertTypes = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };
    
    const alertClass = alertTypes[type] || alertTypes['info'];
    
    const alertElement = document.createElement('div');
    alertElement.className = `alert ${alertClass} alert-dismissible fade show`;
    alertElement.style.minWidth = '300px';
    alertElement.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alertElement);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertElement.parentNode) {
            alertElement.remove();
        }
    }, 5000);
}

// Initialize event listeners when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Update date range when period type changes
    const periodTypeElement = document.getElementById('periodType');
    if (periodTypeElement) {
        periodTypeElement.addEventListener('change', updateDateRange);
    }
    
    // Initialize date validation
    const dateStartElement = document.getElementById('dateStart');
    const dateEndElement = document.getElementById('dateEnd');
    
    if (dateStartElement && dateEndElement) {
        dateStartElement.addEventListener('change', function() {
            if (this.value > dateEndElement.value) {
                dateEndElement.value = this.value;
                showAlert('วันที่สิ้นสุดได้รับการปรับให้ตรงกับวันที่เริ่มต้น', 'info');
            }
        });
        
        dateEndElement.addEventListener('change', function() {
            if (this.value < dateStartElement.value) {
                dateStartElement.value = this.value;
                showAlert('วันที่เริ่มต้นได้รับการปรับให้ตรงกับวันที่สิ้นสุด', 'info');
            }
        });
    }
    
    // Format display enhancement
    const reportFormatElement = document.getElementById('reportFormat');
    if (reportFormatElement) {
        reportFormatElement.addEventListener('change', function() {
            const productDetailsCheckbox = document.getElementById('showProductDetails');
            if (productDetailsCheckbox) {
                if (this.value === 'summary') {
                    productDetailsCheckbox.checked = false;
                    productDetailsCheckbox.disabled = true;
                } else {
                    productDetailsCheckbox.disabled = false;
                }
            }
        });
    }
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+P for print
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            const reportModal = document.getElementById('reportModal');
            if (reportModal && reportModal.classList.contains('show')) {
                printReport('print');
            }
        }
        
        // Escape to close modal
        if (e.key === 'Escape') {
            const reportModal = document.getElementById('reportModal');
            if (reportModal && typeof bootstrap !== 'undefined') {
                const modal = bootstrap.Modal.getInstance(reportModal);
                if (modal) {
                    modal.hide();
                }
            }
        }
    });
    
    console.log('Sales Report JavaScript initialized successfully');
});
