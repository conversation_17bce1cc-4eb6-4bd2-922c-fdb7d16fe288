// Account Payable JavaScript Functions
let currentPage = 1;
let currentEditId = null;

// Initialize page
$(document).ready(function() {
    loadSuppliers();
    loadAccountPayables();
    setTodayDate();
});

// Set today's date as default
function setTodayDate() {
    const today = new Date().toISOString().split('T')[0];
    $('#documentDate').val(today);
    $('#paymentDate').val(today);
}

// Load suppliers for dropdown
async function loadSuppliers() {
    try {
        const response = await fetch('api/suppliers.php');
        const result = await response.json();
        
        if (result.success) {
            const supplierSelect = document.getElementById('supplierSelect');
            const filterSupplier = document.getElementById('filterSupplier');
            
            supplierSelect.innerHTML = '<option value="">เลือกเจ้าหนี้</option>';
            filterSupplier.innerHTML = '<option value="">เจ้าหนี้ทั้งหมด</option>';
            
            result.data.forEach(supplier => {
                const option = `<option value="${supplier.id}">${supplier.supplier_code} - ${supplier.fullname}</option>`;
                supplierSelect.innerHTML += option;
                filterSupplier.innerHTML += option;
            });
        }
    } catch (error) {
        console.error('Error loading suppliers:', error);
    }
}

// Load account payables
async function loadAccountPayables(page = 1) {
    try {
        const keyword = $('#searchKeyword').val();
        const status = $('#filterStatus').val();
        const supplierId = $('#filterSupplier').val();
        
        const params = new URLSearchParams({
            action: 'list',
            page: page,
            limit: 10
        });
        
        if (keyword) params.append('keyword', keyword);
        if (status) params.append('status', status);
        if (supplierId) params.append('supplier_id', supplierId);
        
        const response = await fetch(`api/account_payable.php?${params}`);
        const result = await response.json();
        
        if (result.success) {
            displayAccountPayables(result.data);
            updatePagination(result.pagination);
            currentPage = page;
        } else {
            showAlert('เกิดข้อผิดพลาดในการโหลดข้อมูล', 'error');
        }
    } catch (error) {
        console.error('Error loading account payables:', error);
        showAlert('เกิดข้อผิดพลาดในการเชื่อมต่อ', 'error');
    }
}

// Display account payables in table
function displayAccountPayables(data) {
    const tbody = document.getElementById('accountPayableTableBody');
    
    if (data.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center">ไม่พบข้อมูล</td></tr>';
        return;
    }
    
    tbody.innerHTML = data.map(item => `
        <tr>
            <td>${item.document_no}</td>
            <td>${formatDate(item.document_date)}</td>
            <td>${item.supplier_name}</td>
            <td class="text-end">${formatNumber(item.amount)}</td>
            <td class="text-end">${formatNumber(item.remaining_amount)}</td>
            <td>${formatDate(item.due_date)}</td>
            <td>${getStatusBadge(item.status)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-info" onclick="viewAccountPayable(${item.id})" title="ดู">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-warning" onclick="editAccountPayable(${item.id})" title="แก้ไข">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-success" onclick="showPaymentModal(${item.id})" title="ชำระหนี้">
                        <i class="fas fa-money-bill"></i>
                    </button>
                    <button class="btn btn-danger" onclick="deleteAccountPayable(${item.id})" title="ลบ">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// Update pagination
function updatePagination(pagination) {
    const paginationContainer = document.getElementById('pagination');
    
    if (!pagination || pagination.total_pages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // Previous button
    if (pagination.current_page > 1) {
        paginationHTML += `<li class="page-item">
            <a class="page-link" href="#" onclick="loadAccountPayables(${pagination.current_page - 1})">ก่อนหน้า</a>
        </li>`;
    }
    
    // Page numbers
    for (let i = 1; i <= pagination.total_pages; i++) {
        const active = i === pagination.current_page ? 'active' : '';
        paginationHTML += `<li class="page-item ${active}">
            <a class="page-link" href="#" onclick="loadAccountPayables(${i})">${i}</a>
        </li>`;
    }
    
    // Next button
    if (pagination.current_page < pagination.total_pages) {
        paginationHTML += `<li class="page-item">
            <a class="page-link" href="#" onclick="loadAccountPayables(${pagination.current_page + 1})">ถัดไป</a>
        </li>`;
    }
    
    paginationContainer.innerHTML = paginationHTML;
}

// Show add modal
function showAddModal() {
    currentEditId = null;
    document.getElementById('modalTitle').textContent = 'เพิ่มบัญชีเจ้าหนี้';
    document.getElementById('accountPayableForm').reset();
    document.getElementById('editId').value = '';
    document.getElementById('documentNo').value = 'ระบบจะสร้างอัตโนมัติ';
    setTodayDate();
    $('#accountPayableModal').modal('show');
}

// Edit account payable
async function editAccountPayable(id) {
    try {
        const response = await fetch(`api/account_payable.php?action=get&id=${id}`);
        const result = await response.json();
        
        if (result.success) {
            const data = result.data;
            currentEditId = id;
            
            document.getElementById('modalTitle').textContent = 'แก้ไขบัญชีเจ้าหนี้';
            document.getElementById('editId').value = id;
            document.getElementById('supplierSelect').value = data.supplier_id;
            document.getElementById('documentNo').value = data.document_no;
            document.getElementById('documentDate').value = data.document_date;
            document.getElementById('dueDate').value = data.due_date;
            document.getElementById('refDocumentNo').value = data.ref_document_no || '';
            document.getElementById('refDocumentDate').value = data.ref_document_date || '';
            document.getElementById('amount').value = data.amount;
            document.getElementById('vatAmount').value = data.vat_amount || '';
            document.getElementById('withholdingAmount').value = data.withholding_amount || '';
            document.getElementById('description').value = data.description || '';
            
            $('#accountPayableModal').modal('show');
        } else {
            showAlert('ไม่พบข้อมูลที่ต้องการแก้ไข', 'error');
        }
    } catch (error) {
        console.error('Error loading account payable:', error);
        showAlert('เกิดข้อผิดพลาดในการโหลดข้อมูล', 'error');
    }
}

// Save account payable
async function saveAccountPayable() {
    const form = document.getElementById('accountPayableForm');
    const formData = new FormData(form);
    
    // Validate required fields
    if (!formData.get('supplier_id') || !formData.get('document_date') || 
        !formData.get('due_date') || !formData.get('amount')) {
        showAlert('กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน', 'warning');
        return;
    }
    
    try {
        const method = currentEditId ? 'PUT' : 'POST';
        const response = await fetch('api/account_payable.php', {
            method: method,
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert(currentEditId ? 'แก้ไขข้อมูลเรียบร้อย' : 'เพิ่มข้อมูลเรียบร้อย', 'success');
            $('#accountPayableModal').modal('hide');
            loadAccountPayables(currentPage);
        } else {
            showAlert(result.message || 'เกิดข้อผิดพลาดในการบันทึกข้อมูล', 'error');
        }
    } catch (error) {
        console.error('Error saving account payable:', error);
        showAlert('เกิดข้อผิดพลาดในการเชื่อมต่อ', 'error');
    }
}

// Delete account payable
async function deleteAccountPayable(id) {
    if (!confirm('คุณต้องการลบรายการนี้หรือไม่?')) {
        return;
    }
    
    try {
        const response = await fetch(`api/account_payable.php?id=${id}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('ลบข้อมูลเรียบร้อย', 'success');
            loadAccountPayables(currentPage);
        } else {
            showAlert(result.message || 'เกิดข้อผิดพลาดในการลบข้อมูล', 'error');
        }
    } catch (error) {
        console.error('Error deleting account payable:', error);
        showAlert('เกิดข้อผิดพลาดในการเชื่อมต่อ', 'error');
    }
}

// Show payment modal
async function showPaymentModal(accountPayableId) {
    try {
        const response = await fetch(`api/account_payable.php?action=get&id=${accountPayableId}`);
        const result = await response.json();
        
        if (result.success) {
            const data = result.data;
            document.getElementById('paymentAccountPayableId').value = accountPayableId;
            document.getElementById('paymentAmount').value = data.remaining_amount;
            document.getElementById('paymentAmount').max = data.remaining_amount;
            setTodayDate();
            $('#paymentModal').modal('show');
        }
    } catch (error) {
        console.error('Error loading account payable for payment:', error);
        showAlert('เกิดข้อผิดพลาดในการโหลดข้อมูล', 'error');
    }
}

// Save payment
async function savePayment() {
    const form = document.getElementById('paymentForm');
    const formData = new FormData(form);
    
    // Validate required fields
    if (!formData.get('payment_date') || !formData.get('amount') || !formData.get('payment_method')) {
        showAlert('กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน', 'warning');
        return;
    }
    
    try {
        const response = await fetch('api/account_payable_payment.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('บันทึกการชำระหนี้เรียบร้อย', 'success');
            $('#paymentModal').modal('hide');
            loadAccountPayables(currentPage);
        } else {
            showAlert(result.message || 'เกิดข้อผิดพลาดในการบันทึกการชำระหนี้', 'error');
        }
    } catch (error) {
        console.error('Error saving payment:', error);
        showAlert('เกิดข้อผิดพลาดในการเชื่อมต่อ', 'error');
    }
}

// View account payable details
function viewAccountPayable(id) {
    // TODO: Implement view modal with payment history
    window.open(`account-payable-detail.php?id=${id}`, '_blank');
}

// Utility functions
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('th-TH');
}

function formatNumber(number) {
    if (number === null || number === undefined) return '0.00';
    return parseFloat(number).toLocaleString('th-TH', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function getStatusBadge(status) {
    const statusMap = {
        'unpaid': '<span class="badge bg-danger">ยังไม่ชำระ</span>',
        'partial': '<span class="badge bg-warning">ชำระบางส่วน</span>',
        'paid': '<span class="badge bg-success">ชำระแล้ว</span>',
        'cancelled': '<span class="badge bg-secondary">ยกเลิก</span>'
    };
    return statusMap[status] || '<span class="badge bg-secondary">ไม่ทราบสถานะ</span>';
}

function showAlert(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };
    
    const alertHTML = `
        <div class="alert ${alertClass[type]} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Show alert at top of page
    $('body').prepend(alertHTML);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}

// Event listeners
$('#searchKeyword').on('keypress', function(e) {
    if (e.which === 13) {
        loadAccountPayables();
    }
});

// Auto calculate due date based on supplier credit days
$('#supplierSelect').on('change', async function() {
    const supplierId = this.value;
    if (!supplierId) return;
    
    try {
        const response = await fetch(`api/suppliers.php?id=${supplierId}`);
        const result = await response.json();
        
        if (result.success && result.data.credit_day) {
            const documentDate = new Date($('#documentDate').val());
            documentDate.setDate(documentDate.getDate() + parseInt(result.data.credit_day));
            $('#dueDate').val(documentDate.toISOString().split('T')[0]);
        }
    } catch (error) {
        console.error('Error loading supplier data:', error);
    }
});
