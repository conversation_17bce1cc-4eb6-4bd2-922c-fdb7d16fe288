const { createApp, ref } = Vue
const app = createApp({    setup() {
        const products = ref([]);
        const subTotal = ref(0);
        const discount = ref(0);
        const vat = ref(0);
        const withholding = ref(0);
        const afterDiscount = ref(0);
        const grandTotal = ref(0);
        const invoiceItems = ref([]);
        const documentDueDate = ref('');
        
        // Pagination variables
        const currentPage = ref(1);
        const itemsPerPage = ref(10);
        const totalProducts = ref(0);
        const totalPages = ref(0);
        const selectAll = ref(false);
        const searchQuery = ref('');


        // เลือกสินค้าจาก modal
        const onSelectProduct = () => {
            for(let i = 0; i < products.value.length; i++) {
                if (products.value[i].selected) {                    
                    let qu = {
                        id: 0,
                        product_id: products.value[i].id,
                        product_code: products.value[i].product_code,
                        product_name: products.value[i].product_name,
                        quantity: parseFloat(products.value[i].quantity) || 1, // Use quantity from product modal (editable)
                        unit_name: products.value[i].unit_name,
                        price: products.value[i].price,
                        discount: 0,
                        total: products.value[i].price * (parseFloat(products.value[i].quantity) || 1), // Calculate total based on quantity
                        pdf: products.value[i].profile_image ? $base_url + '/upload_image/product/' + products.value[i].profile_image : null,
                    };
                    invoiceItems.value.push(qu);
                }
            }            $('#productModal').modal('hide');
            products.value.forEach(product => {
                product.selected = false;
            });
            selectAll.value = false;
        };

        // ลบรายการสินค้าในใบเสนอราคา
        const removeReservationItem = (index) => {
            Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
            if (result.isConfirmed) {
                invoiceItems.value.splice(index, 1);
                Swal.fire(
                'Deleted!',
                'Your item has been deleted.',
                'success'
                )
            }
            })
        }

        // คำนวณราคารวม item.total
        const calculateTotal = (item) => {
            let total = item.price * item.quantity;
            if (item.discount > 0) {
                total -= item.discount;
            }
            return total;
        }

        // คำนวณราคารวมทั้งหมด
        const calculateSubTotal = () => {
            let total = 0;
            for (let i = 0; i < invoiceItems.value.length; i++) {
                total += calculateTotal(invoiceItems.value[i]);
            }
            subTotal.value = total;
            return subTotal.value;
        }
        // คำนวณยอดรวมสุทธิ
        const calculateGrandTotal = () => {
            const discount = parseFloat($('#discount').val()) || 0;
            const vatType = parseInt($('#vat_type').val());
            const withholding_type = parseFloat($('#withholding_type').val()) || 0;
            const subTotal = calculateSubTotal();
            
            afterDiscount.value = (subTotal - discount).toFixed(2);            
            // คำนวณ VAT ตาม Type
            if (vatType === 1) {
                // Type 1: ราคาสินค้ารวมภาษี (VAT 7% included)
                vat.value = (afterDiscount.value * 0.07 / 1.07).toFixed(2);
                grandTotal.value = parseFloat(afterDiscount.value).toFixed(2);
                withholding.value = (afterDiscount.value * withholding_type).toFixed(2); // ให้หัก ณ จ่าย สำหรับ Type 1
            } else if (vatType === 2) {
                // Type 2: ราคาสินค้าแยกภาษี (VAT 7% separate)
                vat.value = (afterDiscount.value * 0.07).toFixed(2);
                // คำนวณยอดหัก ณ จ่าย (หักจากยอดหลังหักส่วนลด + withholding_type)
                const totalWithVat = parseFloat(afterDiscount.value) + parseFloat(vat.value);
                withholding.value = (afterDiscount.value * withholding_type).toFixed(2);
                grandTotal.value = (subTotal + parseFloat(vat.value)).toFixed(2);
            } else if (vatType === 3) {
                // Type 3: อัตราภาษี 0% (VAT 0%)
                vat.value = 0;
                withholding.value = (afterDiscount.value * withholding_type).toFixed(2); // ให้หัก ณ จ่าย สำหรับ Type 3
                grandTotal.value = parseFloat(afterDiscount.value).toFixed(2);
            }

            return grandTotal.value;
        };        // คำนวณยอดหัก ณ จ่าย
        const calculateWithholding = () => {
            const withholding_type = parseFloat($('#withholding_type').val()) || 0;
            const vatType = parseInt($('#vat_type').val());
            
            // หัก ณ จ่าย เฉพาะ Type 2 เท่านั้น
            if (vatType === 2) {
                const subTotal = calculateSubTotal();
                const discount = parseFloat($('#discount').val()) || 0;
                const afterDiscount = subTotal - discount;
                const vat = afterDiscount * 0.07;
                const totalWithVat = afterDiscount + vat;
                withholding.value = (totalWithVat * withholding_type).toFixed(2);
            } else {
                withholding.value = 0;
            }
            
            return withholding.value;
        };        // คำนวณราคารวม item.grand_total negative withholding_type
        const calculateGrandTotalMinusWithholding = () => {
            const vatType = parseInt($('#vat_type').val());
            const withholding_type = parseFloat($('#withholding_type').val()) || 0;
            const discount = parseFloat($('#discount').val()) || 0;
            
            let grandTotal = 0;
            for (let i = 0; i < invoiceItems.value.length; i++) {
                grandTotal += calculateTotal(invoiceItems.value[i]);
            }
            
            const afterDiscount = grandTotal - discount;
            if (vatType === 1) {
                // Type 1: ราคาสินค้ารวมภาษี (ไม่หัก ณ จ่าย)
                // ยอดรวมหลังหักภาษี = รวมจำนวนเงิน - ภาษีมูลค่าเพิ่ม
                const vat = afterDiscount * 0.07 / 1.07;
                return (afterDiscount - vat).toFixed(2);
            } else if (vatType === 2) {
                // Type 2: ราคาสินค้าแยกภาษี (หัก ณ จ่าย)
                const vat = afterDiscount * 0.07;
                const totalWithVat = afterDiscount + vat;
                const withholdingAmount = afterDiscount * withholding_type;
                const grandTotalMinusWithholding = totalWithVat - withholdingAmount;
                return grandTotalMinusWithholding.toFixed(2);
            } else {
                // Type 3: อัตราภาษี 0% (ไม่หัก ณ จ่าย)
                return afterDiscount.toFixed(2);
            }
        }


        const fetchProductsByReservation = (reservation_id, page = 1, search = '') => {
            if (!reservation_id || reservation_id <= 0) {
                products.value = [];
                selectAll.value = false;
                currentPage.value = 1;
                totalPages.value = 0;
                totalProducts.value = 0;
                return;
            }
            
            currentPage.value = page;
            const offset = (page - 1) * itemsPerPage.value;
            let urlGetProduct = $base_url + '/api/product.php?reservation_id=' + reservation_id + '&limit=' + itemsPerPage.value + '&offset=' + offset;
            let urlGetProductCount = $base_url + '/api/product.php?reservation_id=' + reservation_id + '&count_only=1';
            
            // Add search parameter if provided
            if (search && search.trim() !== '') {
                urlGetProduct += '&search=' + encodeURIComponent(search);
                urlGetProductCount += '&search=' + encodeURIComponent(search);
            }
            
            // Get total count first
            $.get(urlGetProductCount, function(countData) {
                try {
                    const countResult = JSON.parse(countData);
                    totalProducts.value = countResult.total || 0;
                    totalPages.value = Math.ceil(totalProducts.value / itemsPerPage.value);
                } catch (e) {
                    console.error("Error parsing count response:", e);
                    totalProducts.value = 0;
                    totalPages.value = 0;
                }
            }).fail(function() {
                totalProducts.value = 0;
                totalPages.value = 0;
            });
              // Get products for current page
            $.get(urlGetProduct, function(data){
                try {
                    let productsData = JSON.parse(data);
                    for (let i = 0; i < productsData.length; i++) {
                        productsData[i].selected = false;
                        // ใช้ quantity จาก product ถ้ามี
                        productsData[i].quantity = productsData[i].quantity || 1;
                    }
                    products.value = productsData;
                    selectAll.value = false;
                } catch (e) {
                    console.error("Error parsing JSON response:", e);
                    console.log("Raw response:", data);
                    // Show error to user
                    Swal.fire({
                        title: 'Error',
                        text: 'Failed to load product data. Please try again or contact support.',
                        icon: 'error'
                    });
                    products.value = [];
                }
            }).fail(function(_, textStatus, errorThrown) {
                console.error("AJAX request failed:", textStatus, errorThrown);
                Swal.fire({
                    title: 'Error',
                    text: 'Failed to connect to the server. Please check your connection and try again.',
                    icon: 'error'
                });
                products.value = [];
            });
        };

        // Fetch invoice details
        const fetchInvoiceDetails = () => {
            const invoiceId = new URLSearchParams(window.location.search).get('id');
            if (!invoiceId) return;

            fetch(`${$base_url}/api/invoice-detail.php?invoice_id=${invoiceId}`)
                .then(response => response.json())
                .then(data => {
                    invoiceItems.value = data.map(item => ({
                        ...item,
                        pdf: item.profile_image ? `${$base_url}/upload_image/product/${item.profile_image}` : null
                    }));
                })
                .catch(error => console.error('Error fetching invoice details:', error));        };        // Search function (Enter key or search button)
        const searchProducts = (event) => {
            // Prevent form submission when Enter is pressed or search button is clicked
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            const reservationId = $('#reservation_id').val();
            if (reservationId > 0) {
                fetchProductsByReservation(reservationId, 1, searchQuery.value);
            }
        };// Pagination functions
        const goToPage = (page, event) => {
            // Prevent form submission
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            if (page >= 1 && page <= totalPages.value) {
                const reservationId = $('#reservation_id').val();
                if (reservationId > 0) {
                    fetchProductsByReservation(reservationId, page, searchQuery.value);
                }
            }
        };const prevPage = (event) => {
            // Prevent form submission
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            if (currentPage.value > 1) {
                goToPage(currentPage.value - 1, event);
            }
        };

        const nextPage = (event) => {
            // Prevent form submission
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            if (currentPage.value < totalPages.value) {
                goToPage(currentPage.value + 1, event);
            }
        };

        // Select All functionality
        const toggleSelectAll = () => {
            products.value.forEach(product => {
                product.selected = selectAll.value;
            });
        };

        const updateSelectAll = () => {
            const selectedCount = products.value.filter(product => product.selected).length;
            const totalCount = products.value.length;
            selectAll.value = selectedCount === totalCount && totalCount > 0;
        };

        // Remove an item from the invoice
        const removeInvoiceItem = (index) => {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    invoiceItems.value.splice(index, 1);
                    Swal.fire('Deleted!', 'Your item has been deleted.', 'success');
                }
            });
        };        return {
            subTotal,
            vat,
            afterDiscount,
            discount,
            grandTotal,
            withholding,
            invoiceItems,
            products,
            documentDueDate,
            calculateGrandTotal,
            calculateWithholding,
            onSelectProduct,
            removeReservationItem,
            calculateTotal,
            calculateSubTotal,
            calculateGrandTotalMinusWithholding,
            fetchProductsByReservation,
            fetchInvoiceDetails,
            removeInvoiceItem,
            // Pagination
            currentPage,
            itemsPerPage,
            totalProducts,
            totalPages,
            goToPage,
            prevPage,
            nextPage,
            // Select All
            selectAll,
            toggleSelectAll,
            updateSelectAll,
            // Search
            searchQuery,
            searchProducts
        }
    },    mounted() {
        this.fetchInvoiceDetails();
        const reservationId = $('#reservation_id').val();
        if (reservationId > 0) {
            this.fetchProductsByReservation(reservationId);
        }
    }
}).mount('#appvue');

//## jquery section ##
$(function(){
    $('#customer_id').on('change', function(){
        var customer_id = $(this).val();
        var urlGetCustomer = $base_url + '/api/customer.php?id=' + customer_id;
        var urlReservation = $base_url + '/api/reservation.php?customer_id=' + customer_id;

        let customer;
        $.get(urlGetCustomer).then((data) => {
            customer = JSON.parse(data);
             const vatType = customer.vat_type==1 ? 'Vat 7% (รวมภาษี)' : customer.vat_type==2 ? 'Vat 7% (แยกภาษี)' : 'Vat 0%';
            $('#short_name').val(customer.short_name);
            $('#customer_name').val(customer.fullname);
            $('#contact_name').val(customer.contact_name);
            $('#credit_day').val(customer.credit_day);
            $('#payment_type').val(customer.payment_type);
            $('#vat_type').val(customer.vat_type);
            $('#vat_type_name').val(vatType);
            $('#withholding_type').val(customer.withholding_type);

           // Calculate due date again when reservation is selected
           var document_date = $('#document_date').val();
           var credit_day = $('#credit_day').val();

           if (document_date && credit_day) {
               // Convert Thai date format (DD/MM/YYYY) to Date object
               const [day, month, year] = document_date.split('/');
               const dateObj = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

               // Add credit days
               dateObj.setDate(dateObj.getDate() + parseInt(credit_day || 0));

               // Format for display (DD/MM/YYYY)
               const formattedDay = String(dateObj.getDate()).padStart(2, '0');
               const formattedMonth = String(dateObj.getMonth() + 1).padStart(2, '0');
               const formattedYear = dateObj.getFullYear();
               const formattedDueDate = `${formattedDay}/${formattedMonth}/${formattedYear}`;

               // Set the input value and update Vue app
               $('#document_due_date').val(formattedDueDate);

               // Update the Vue data
               const vueApp = document.getElementById('appvue').__vue_app__;
               if (vueApp && vueApp._instance) {
                   const vueInstance = vueApp._instance.proxy;
                   if (vueInstance && vueInstance.documentDueDate !== undefined) {
                       vueInstance.documentDueDate = formattedDueDate;
                   }
               }
           }

            // Fetch reservations after customer data is loaded
            return $.get(urlReservation);
            }).then((data) => {
            let reservations = JSON.parse(data);
            $('#reservation_id').empty();
            $('#reservation_id').append('<option value="">-- เลือกใบจอง PO --</option>');
                for (let i = 0; i < reservations.length; i++) {
                    $('#reservation_id').append('<option value="' + reservations[i].id + '">' + reservations[i].document_number + ' - ' + customer.short_name + ' - ' + reservations[i].purchase_order + '</option>');
                }
            })
            .catch((error) => {
                console.error('Error fetching data:', error);
            });

    });    $('#reservation_id').on('change', function(){
        var reservation_id = $(this).val();
        
        // Use Vue.js pagination function instead of direct jQuery
        if (reservation_id > 0) {
            app.searchQuery = ''; // Reset search when reservation changes
            app.fetchProductsByReservation(reservation_id, 1);
        } else {
            app.products = [];
            app.selectAll = false;
            app.currentPage = 1;
            app.totalPages = 0;
            app.totalProducts = 0;
            app.searchQuery = '';
        }
    });

    //เมื่อ reservation_id จะ ข้อมูล purchase_order มาแสดงใน
    $('#reservation_id').on('change', function() {
        var reservation_id = $(this).val();
        var urlGetReservation = $base_url + '/api/reservation.php?id=' + reservation_id;

        $.get(urlGetReservation).then((data) => {
            let reservations = JSON.parse(data);
            if (reservations.length > 0) {
                $('#purchase_order').val(reservations[0].purchase_order);

                // Calculate due date again when reservation is selected
                var document_date = $('#document_date').val();
                var credit_day = $('#credit_day').val();

                if (document_date && credit_day) {
                    // Convert Thai date format (DD/MM/YYYY) to Date object
                    const [day, month, year] = document_date.split('/');
                    const dateObj = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

                    // Add credit days
                    dateObj.setDate(dateObj.getDate() + parseInt(credit_day || 0));

                    // Format for display (DD/MM/YYYY)
                    const formattedDay = String(dateObj.getDate()).padStart(2, '0');
                    const formattedMonth = String(dateObj.getMonth() + 1).padStart(2, '0');
                    const formattedYear = dateObj.getFullYear();
                    const formattedDueDate = `${formattedDay}/${formattedMonth}/${formattedYear}`;

                    // Set the input value and update Vue app
                    $('#document_due_date').val(formattedDueDate);

                    // Update the Vue data
                    const vueApp = document.getElementById('appvue').__vue_app__;
                    if (vueApp && vueApp._instance) {
                        const vueInstance = vueApp._instance.proxy;
                        if (vueInstance && vueInstance.documentDueDate !== undefined) {
                            vueInstance.documentDueDate = formattedDueDate;
                        }
                    }
                }
            } else {
                $('#purchase_order').val(''); // Clear the field if no data is found
            }
        }).catch((error) => {
            console.error('Error fetching reservation data:', error);
            $('#purchase_order').val(''); // Clear the field in case of an error
        });
    });


    // เมื่อกดปุ่มลบในรายการ จะแจ้งข้อความ
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        let urlDelete = $(this).attr('href');
        Swal.fire({
            text: "การลบข้อมูล?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'ลบข้อมูล!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = urlDelete;
            }
        });    });
    
    // Prevent form submission when Enter is pressed, except on submit buttons
    $('form').on('keypress', function(e) {
        if (e.which === 13 && e.target.type !== 'submit' && e.target.type !== 'button') {
            e.preventDefault();
            return false;
        }
    });

    // Prevent form submission when pressing Enter in the modal
    $(document).on('keydown', '#productModal input[type="text"]', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    });

    // Prevent form submission when clicking pagination buttons in modal
    $(document).on('click', '#productModal .pagination button', function(e) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    });

    // Prevent form submission when clicking search button in modal
    $(document).on('click', '#productModal .btn-outline-secondary', function(e) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    });
});
