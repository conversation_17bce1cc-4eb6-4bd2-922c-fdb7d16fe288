-- สคีมาสำหรับระบบบัญชีเจ้าหนี้ (Account Payable System)



-- ตารางสำหรับรับวางบิล
CREATE TABLE `bill_acceptances` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bill_number` varchar(50) NOT NULL COMMENT 'เลขที่ใบรับวางบิล',
  `supplier_id` int(11) NOT NULL COMMENT 'รหัสเจ้าหนี้/ผู้ขาย',
  `bill_date` date NOT NULL COMMENT 'วันที่รับบิล',
  `invoice_number` varchar(100) DEFAULT NULL COMMENT 'เลขที่ใบแจ้งหนี้จากผู้ขาย',
  `invoice_date` date DEFAULT NULL COMMENT 'วันที่ใบแจ้งหนี้',
  `due_date` date NOT NULL COMMENT 'วันที่ครบกำหนดชำระ',
  `subtotal` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'ยอดรวมก่อนภาษี',
  `vat_rate` decimal(5,2) NOT NULL DEFAULT 7.00 COMMENT 'อัตราภาษีมูลค่าเพิ่ม',
  `vat_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนภาษีมูลค่าเพิ่ม',
  `total_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินรวม',
  `withholding_tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'อัตราภาษีหัก ณ ที่จ่าย',
  `withholding_tax` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'ภาษีหัก ณ ที่จ่าย',
  `net_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'จำนวนเงินสุทธิ',
  `status` enum('pending','approved','rejected','converted') NOT NULL DEFAULT 'pending' COMMENT 'สถานะ',
  `description` text DEFAULT NULL COMMENT 'หมายเหตุ',
  `created_by` int(11) NOT NULL COMMENT 'ผู้สร้าง',
  `approved_by` int(11) DEFAULT NULL COMMENT 'ผู้อนุมัติ',
  `approved_at` datetime DEFAULT NULL COMMENT 'วันที่อนุมัติ',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_status` (`status`),
  KEY `idx_bill_date` (`bill_date`),
  UNIQUE KEY `unique_bill_number` (`bill_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


