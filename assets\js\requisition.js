const { createApp, ref } = Vue
const app = createApp({
    setup() {        
        const requisitionItems = ref([]);
        const products = ref([]);
        const subTotal = ref(0);
        const vat = ref(0); 
        const afterDiscount = ref(0);
        const grandTotal = ref(0);
        
        // Pagination variables
        const currentPage = ref(1);
        const itemsPerPage = ref(10);
        const totalProducts = ref(0);
        const totalPages = ref(0);
        const selectAll = ref(false);
        const searchQuery = ref('');
        
        // เลือกสินค้าจาก modal
        const onSelectProduct = () => {
            for(let i = 0; i < products.value.length; i++) {
                if (products.value[i].selected) {
                    let qu = {
                        id: 0,
                        product_id: products.value[i].id,
                        product_code: products.value[i].product_code,
                        product_name: products.value[i].product_name,
                        quantity: products.value[i].quantity || 1, // Use the quantity from products or default to 1
                        unit_name: products.value[i].unit_name,
                        price: products.value[i].price,
                        discount: 0,
                        total: (products.value[i].quantity || 1) * products.value[i].price, // Calculate total based on quantity
                        detail:products.value[i].detail,
                        pdf: products.value[i].profile_image ? $base_url + '/upload_image/product/' + products.value[i].profile_image : null,
                    };
                    requisitionItems.value.push(qu);
                }
            }            $('#productModal').modal('hide');            
            products.value.forEach(product => {
                product.selected = false;
            });
            selectAll.value = false;
        };        // ลบรายการสินค้าในใบขอซัพพลายเออร์
        const removeRequisitionItem = (index) => {
            Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
            if (result.isConfirmed) {
                requisitionItems.value.splice(index, 1);
            }
            })
        }

        // คำนวณราคารวม item.total
        const calculateTotal = (item) => {
            let total = item.price * item.quantity;
            if (item.discount > 0) {
                total -= item.discount;
            }
            return total;
        }

        // คำนวณราคารวมทั้งหมด
        const calculateSubTotal = () => {
            let total = 0;
            for (let i = 0; i < requisitionItems.value.length; i++) {
                total += calculateTotal(requisitionItems.value[i]);
            }
            subTotal.value = total;
            return subTotal.value;
        }

        // คำนวณยอดรวมสุทธิ
        const calculateGrandTotal = () => {
            const discount = parseFloat($('#discount').val()) || 0;
            const vatType = parseInt($('#vat_type').val());
            const subTotal = calculateSubTotal();
            afterDiscount.value = (subTotal - discount).toFixed(2);
                        
            if (vatType === 1) {
                // VAT 7% รวมภาษี - VAT is already included in the price
                // Calculate VAT amount from total (reverse calculation)
                vat.value = (afterDiscount.value * 0.07 / 1.07).toFixed(2);
                grandTotal.value = parseFloat(afterDiscount.value).toFixed(2);
            } else if (vatType === 2) {
                // VAT 7% แยกภาษี - VAT is separate from price
                vat.value = (afterDiscount.value * 0.07).toFixed(2);
                grandTotal.value = (parseFloat(afterDiscount.value) + parseFloat(vat.value)).toFixed(2);
            } else if (vatType === 3) {
                // VAT 0% - No VAT
                vat.value = 0;
                grandTotal.value = parseFloat(afterDiscount.value).toFixed(2);
            } else {
                // Default case - No VAT
                vat.value = 0;
                grandTotal.value = parseFloat(afterDiscount.value).toFixed(2);
            }

            return grandTotal.value;
        }

        // ดึงรายละเอียดใบขอซัพพลายเออร์
        const fetchRequisitionDetails = () => {            
            const urlParams = new URLSearchParams(window.location.search);
            const requisitionId = urlParams.get('id');

            if(!requisitionId) return;

            const urlGetRequisitionDetails = $base_url + '/api/requisition-detail.php?requisition_id=' + requisitionId;
            $.get(urlGetRequisitionDetails, function(data) {
                const details = JSON.parse(data);                
                requisitionItems.value = [];
                for (let i = 0; i < details.length; i++) {
                    const item = details[i];
                    requisitionItems.value.push({
                        id: item.id,
                        product_id: item.product_id,
                        product_code: item.product_code,
                        product_name: item.product_name,
                        quantity: item.quantity,
                        unit_name: item.unit_name,
                        price: item.price,
                        discount: item.discount,
                        total: item.total,
                        pdf: item.profile_image ? $base_url + '/upload_image/product/' + item.profile_image : null,
                        detail: item.detail,
                    });
                }
                
                calculateGrandTotal();
            });
        };        const fetchProductsBySupplier = (supplier_id, page = 1, search = '') => {
            currentPage.value = page;
            const offset = (page - 1) * itemsPerPage.value;
            let urlGetProduct = $base_url + '/api/product-order.php?supplier_id=' + supplier_id + '&limit=' + itemsPerPage.value + '&offset=' + offset;
            let urlGetProductCount = $base_url + '/api/product-order.php?supplier_id=' + supplier_id + '&count_only=1';
            
            // Add search parameter if provided
            if (search && search.trim() !== '') {
                urlGetProduct += '&search=' + encodeURIComponent(search);
                urlGetProductCount += '&search=' + encodeURIComponent(search);
            }
            
            // Get total count first
            $.get(urlGetProductCount, function(countData) {
                const countResult = JSON.parse(countData);
                totalProducts.value = countResult.total;
                totalPages.value = Math.ceil(totalProducts.value / itemsPerPage.value);
            });
            
            // Get products for current page
            $.get(urlGetProduct, function(data) {
                let productsData = JSON.parse(data);
                for (let i = 0; i < productsData.length; i++) {
                    productsData[i].selected = false;
                }
                products.value = productsData;
                selectAll.value = false;
            });
        };

        // Search function
        const searchProducts = () => {
            const supplierId = $('#supplier_id').val();
            if (supplierId > 0) {
                fetchProductsBySupplier(supplierId, 1, searchQuery.value);
            }
        };

        // Pagination functions
        const goToPage = (page) => {
            if (page >= 1 && page <= totalPages.value) {
                const supplierId = $('#supplier_id').val();
                if (supplierId > 0) {
                    fetchProductsBySupplier(supplierId, page, searchQuery.value);
                }
            }
        };

        const prevPage = () => {
            if (currentPage.value > 1) {
                goToPage(currentPage.value - 1);
            }
        };

        const nextPage = () => {
            if (currentPage.value < totalPages.value) {
                goToPage(currentPage.value + 1);
            }
        };

        // Select All functionality
        const toggleSelectAll = () => {
            products.value.forEach(product => {
                product.selected = selectAll.value;
            });
        };

        const updateSelectAll = () => {
            const selectedCount = products.value.filter(product => product.selected).length;
            const totalCount = products.value.length;
            selectAll.value = selectedCount === totalCount && totalCount > 0;
        };        return {
            subTotal,
            vat,
            afterDiscount,
            grandTotal,
            requisitionItems,
            products,
            calculateGrandTotal,
            onSelectProduct,
            removeRequisitionItem,
            calculateTotal,
            calculateSubTotal,
            fetchRequisitionDetails,
            fetchProductsBySupplier,
            // Pagination
            currentPage,
            itemsPerPage,
            totalProducts,
            totalPages,
            goToPage,
            prevPage,
            nextPage,
            // Select All
            selectAll,
            toggleSelectAll,
            updateSelectAll,
            // Search
            searchQuery,
            searchProducts
        }
    },    mounted() {
        const supplierId = $('#supplier_id').find(':selected').val();        
        if (supplierId > 0) {
            this.fetchProductsBySupplier(supplierId, 1);
        }
        this.fetchRequisitionDetails();        
    }
}).mount('#appvue');

//## jquery section ##
$(function(){
    $('#supplier_id').on('change', function(){
        var supplier_id = $(this).val();        
        var urlGetSupplier = $base_url + '/api/supplier.php?id=' + supplier_id;
        var urlGetProduct = $base_url + '/api/product-order.php?supplier_id=' + supplier_id;

        // get supplier
        $.get(urlGetSupplier, function(data){
            let supplier = JSON.parse(data);
             const vatType = supplier.vat_type==1 ? 'Vat 7% (รวมภาษี)' : supplier.vat_type==2 ? 'Vat 7% (แยกภาษี)' : 'Vat 0%';
            $('#short_name').val(supplier.short_name);
            $('#supplier_name').val(supplier.fullname);
            $('#contact_name').val(supplier.contact_name);
            $('#credit_day').val(supplier.credit_day);
            $('#payment_type').val(supplier.payment_type);
            $('#vat_type').val(supplier.vat_type);
            $('#vat_type_name').val(vatType);
        });        // Use Vue.js pagination function instead of direct jQuery
        if (supplier_id > 0) {
            app.searchQuery = ''; // Reset search when supplier changes
            app.fetchProductsBySupplier(supplier_id, 1);
        } else {
            app.products = [];
            app.selectAll = false;
            app.currentPage = 1;
            app.totalPages = 0;
            app.totalProducts = 0;
            app.searchQuery = '';
        }
    });   
    
    // เมื่อกดปุ่มลบในรายการ จะแจ้งข้อความ
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        let urlDelete = $(this).attr('href');
        Swal.fire({            
            text: "ยืนยันการลบข้อมูล?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'ลบข้อมูล!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = urlDelete;
            }
        });
    });
});
