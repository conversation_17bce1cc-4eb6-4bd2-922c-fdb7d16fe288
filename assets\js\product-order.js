//ลบสินค้า และฟังก์ชันเสริมสำหรับ product orders list

// ฟังก์ชันแสดงข้อมูลการนำทาง
function showNavigationInfo() {
    const urlParams = new URLSearchParams(window.location.search);
    const currentPage = parseInt(urlParams.get('page')) || 1;
    console.log('หน้าปัจจุบัน:', currentPage);
    console.log('คีย์บอร์ดช็อตคัท:');
    console.log('- Ctrl + ← : หน้าก่อนหน้า');
    console.log('- Ctrl + → : หน้าถัดไป');
    console.log('- Ctrl + Home : หน้าแรก');
    console.log('- Ctrl + End : หน้าสุดท้าย');
}

// ฟังก์ชันเสริมสำหรับการจัดการ URL parameters
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// ฟังก์ชันสำหรับอัปเดต URL parameters
function updateUrlParameter(param, value) {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set(param, value);
    return window.location.pathname + '?' + urlParams.toString();
}

// ฟังก์ชันสำหรับลบ URL parameter
function removeUrlParameter(param) {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.delete(param);
    return window.location.pathname + '?' + urlParams.toString();
}

// ฟังก์ชันการนำทางด้วยคีย์บอร์ด
function setupKeyboardNavigation() {
    const urlParams = new URLSearchParams(window.location.search);
    const currentPage = parseInt(urlParams.get('page')) || 1;
    const searchKeyword = urlParams.get('search_keyword') || '';
    const categoryId = urlParams.get('category_id') || '';
    const sortBy = urlParams.get('sort_by') || 'id';
    const sortOrder = urlParams.get('sort_order') || 'desc';
    
    // สร้าง base URL สำหรับการนำทาง
    function createNavigationUrl(page) {
        const params = new URLSearchParams();
        params.set('page', page);
        if (searchKeyword) params.set('search_keyword', searchKeyword);
        if (categoryId) params.set('category_id', categoryId);
        params.set('sort_by', sortBy);
        params.set('sort_order', sortOrder);
        return window.location.pathname + '?' + params.toString();
    }
    
    // ตรวจสอบจำนวนหน้าทั้งหมด
    const paginationItems = document.querySelectorAll('.pagination .page-item:not(.disabled) .page-link');
    let totalPages = 1;
    paginationItems.forEach(item => {
        const pageNum = parseInt(item.textContent);
        if (!isNaN(pageNum) && pageNum > totalPages) {
            totalPages = pageNum;
        }
    });
    
    document.addEventListener('keydown', function(event) {
        // ตรวจสอบว่าผู้ใช้กำลังพิมพ์ในฟิลด์ input หรือไม่
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA' || event.target.tagName === 'SELECT') {
            return;
        }
        
        if (event.ctrlKey) {
            switch(event.key) {
                case 'ArrowLeft': // Ctrl + ← (หน้าก่อนหน้า)
                    event.preventDefault();
                    if (currentPage > 1) {
                        window.location.href = createNavigationUrl(currentPage - 1);
                    }
                    break;
                    
                case 'ArrowRight': // Ctrl + → (หน้าถัดไป)
                    event.preventDefault();
                    if (currentPage < totalPages) {
                        window.location.href = createNavigationUrl(currentPage + 1);
                    }
                    break;
                    
                case 'Home': // Ctrl + Home (หน้าแรก)
                    event.preventDefault();
                    if (currentPage !== 1) {
                        window.location.href = createNavigationUrl(1);
                    }
                    break;
                    
                case 'End': // Ctrl + End (หน้าสุดท้าย)
                    event.preventDefault();
                    if (currentPage !== totalPages) {
                        window.location.href = createNavigationUrl(totalPages);
                    }
                    break;
            }
        }
    });
    
    // แสดงข้อมูลการนำทางใน console
    console.log(`หน้าปัจจุบัน: ${currentPage}/${totalPages}`);
    console.log('การนำทางด้วยคีย์บอร์ด:');
    console.log('- Ctrl + ← : หน้าก่อนหน้า');
    console.log('- Ctrl + → : หน้าถัดไป'); 
    console.log('- Ctrl + Home : หน้าแรก');
    console.log('- Ctrl + End : หน้าสุดท้าย');
}

// ฟังก์ชันเพิ่มเติมสำหรับการจัดการ pagination
function goToPage(page) {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('page', page);
    window.location.href = window.location.pathname + '?' + urlParams.toString();
}

function goToFirstPage() {
    goToPage(1);
}

function goToLastPage() {
    // หาหน้าสุดท้ายจาก pagination
    const paginationItems = document.querySelectorAll('.pagination .page-item:not(.disabled) .page-link');
    let lastPage = 1;
    paginationItems.forEach(item => {
        const pageNum = parseInt(item.textContent);
        if (!isNaN(pageNum) && pageNum > lastPage) {
            lastPage = pageNum;
        }
    });
    goToPage(lastPage);
}

function goToNextPage() {
    const currentPage = parseInt(getUrlParameter('page')) || 1;
    goToPage(currentPage + 1);
}

function goToPreviousPage() {
    const currentPage = parseInt(getUrlParameter('page')) || 1;
    if (currentPage > 1) {
        goToPage(currentPage - 1);
    }
}

// เมื่อโหลดหน้าเสร็จ แสดงข้อมูลการนำทาง
document.addEventListener('DOMContentLoaded', function() {
    console.log('Product Orders JS loaded');
    
    // ตั้งค่าการนำทางด้วยคีย์บอร์ด
    setupKeyboardNavigation();
    
    // เพิ่ม tooltip สำหรับปุ่ม pagination
    const paginationLinks = document.querySelectorAll('.pagination .page-link');
    paginationLinks.forEach(link => {
        if (link.title) {
            link.setAttribute('data-bs-toggle', 'tooltip');
            link.setAttribute('data-bs-placement', 'top');
        }
    });
    
    // เริ่มต้น Bootstrap tooltips (ถ้ามี)
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
});

